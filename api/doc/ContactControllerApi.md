# openapi.api.ContactControllerApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getUsers**](ContactControllerApi.md#getusers) | **POST** /contact/get | 


# **getUsers**
> BuiltList<TbUser> getUsers(requestBody)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getContactControllerApi();
final BuiltList<String> requestBody = ; // BuiltList<String> | 

try {
    final response = api.getUsers(requestBody);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ContactControllerApi->getUsers: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **requestBody** | [**BuiltList&lt;String&gt;**](String.md)|  | 

### Return type

[**BuiltList&lt;TbUser&gt;**](TbUser.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

