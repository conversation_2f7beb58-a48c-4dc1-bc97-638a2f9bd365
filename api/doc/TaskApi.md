# openapi.api.TaskApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getAssignedTasks**](TaskApi.md#getassignedtasks) | **GET** /task/getAssignedTasks | 
[**getReceivedTasks**](TaskApi.md#getreceivedtasks) | **GET** /task/getReceivedTasks | 
[**getTasksInPlan**](TaskApi.md#gettasksinplan) | **GET** /task/getTasksInPlan | 
[**upsertTask**](TaskApi.md#upserttask) | **PUT** /task/upsertTask | 


# **getAssignedTasks**
> BuiltList<TbTask> getAssignedTasks()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getTaskApi();

try {
    final response = api.getAssignedTasks();
    print(response);
} catch on DioException (e) {
    print('Exception when calling TaskApi->getAssignedTasks: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbTask&gt;**](TbTask.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getReceivedTasks**
> BuiltList<TbTask> getReceivedTasks()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getTaskApi();

try {
    final response = api.getReceivedTasks();
    print(response);
} catch on DioException (e) {
    print('Exception when calling TaskApi->getReceivedTasks: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbTask&gt;**](TbTask.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getTasksInPlan**
> BuiltList<TbTask> getTasksInPlan(planId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getTaskApi();
final String planId = planId_example; // String | 

try {
    final response = api.getTasksInPlan(planId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling TaskApi->getTasksInPlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **planId** | **String**|  | 

### Return type

[**BuiltList&lt;TbTask&gt;**](TbTask.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upsertTask**
> bool upsertTask(createTaskRequest)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getTaskApi();
final CreateTaskRequest createTaskRequest = ; // CreateTaskRequest | 

try {
    final response = api.upsertTask(createTaskRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling TaskApi->upsertTask: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createTaskRequest** | [**CreateTaskRequest**](CreateTaskRequest.md)|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

