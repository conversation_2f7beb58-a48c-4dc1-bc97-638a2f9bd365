# openapi.api.BudgetPlanningApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**budgetPlanningItem**](BudgetPlanningApi.md#budgetplanningitem) | **GET** /budget-planning/_budgetPlanningItem | 
[**getBudgetConst**](BudgetPlanningApi.md#getbudgetconst) | **GET** /budget-planning/getBudgetConst | 
[**getBudgetFactors**](BudgetPlanningApi.md#getbudgetfactors) | **GET** /budget-planning/getBudgetFactors | 
[**getRoomConst**](BudgetPlanningApi.md#getroomconst) | **GET** /budget-planning/getRoomConst | 
[**plan**](BudgetPlanningApi.md#plan) | **POST** /budget-planning/plan | 
[**setBudgetConstFixedDeviceFactor**](BudgetPlanningApi.md#setbudgetconstfixeddevicefactor) | **POST** /budget-planning/setBudgetConstFixedDevice | 
[**setBudgetConstLabor**](BudgetPlanningApi.md#setbudgetconstlabor) | **POST** /budget-planning/setBudgetConstLabor | 
[**setBudgetConstMaterial**](BudgetPlanningApi.md#setbudgetconstmaterial) | **POST** /budget-planning/setBudgetConstMaterial | 
[**setBudgetFactor**](BudgetPlanningApi.md#setbudgetfactor) | **POST** /budget-planning/setBudgetFactor | 
[**setRoomConstLabor**](BudgetPlanningApi.md#setroomconstlabor) | **POST** /budget-planning/setRoomConstLabor | 
[**setRoomConstMaterial**](BudgetPlanningApi.md#setroomconstmaterial) | **POST** /budget-planning/setRoomConstMaterial | 


# **budgetPlanningItem**
> BudgetPlanningItem budgetPlanningItem()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();

try {
    final response = api.budgetPlanningItem();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->budgetPlanningItem: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BudgetPlanningItem**](BudgetPlanningItem.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getBudgetConst**
> BuiltList<TbBudgetConst> getBudgetConst()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();

try {
    final response = api.getBudgetConst();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->getBudgetConst: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbBudgetConst&gt;**](TbBudgetConst.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getBudgetFactors**
> BuiltList<TbBudgetFactor> getBudgetFactors()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();

try {
    final response = api.getBudgetFactors();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->getBudgetFactors: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbBudgetFactor&gt;**](TbBudgetFactor.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getRoomConst**
> BuiltList<TbBudgetRoomConst> getRoomConst()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();

try {
    final response = api.getRoomConst();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->getRoomConst: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbBudgetRoomConst&gt;**](TbBudgetRoomConst.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **plan**
> BuiltList<String> plan(budgetPlanningParameter)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();
final BudgetPlanningParameter budgetPlanningParameter = ; // BudgetPlanningParameter | 

try {
    final response = api.plan(budgetPlanningParameter);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->plan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **budgetPlanningParameter** | [**BudgetPlanningParameter**](BudgetPlanningParameter.md)|  | 

### Return type

**BuiltList&lt;String&gt;**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: text/event-stream

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setBudgetConstFixedDeviceFactor**
> bool setBudgetConstFixedDeviceFactor(typeId, fixedDeviceFactor)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();
final String typeId = typeId_example; // String | 
final int fixedDeviceFactor = 56; // int | 

try {
    final response = api.setBudgetConstFixedDeviceFactor(typeId, fixedDeviceFactor);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->setBudgetConstFixedDeviceFactor: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **typeId** | **String**|  | 
 **fixedDeviceFactor** | **int**|  | [optional] 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setBudgetConstLabor**
> bool setBudgetConstLabor(typeId, laborFactor)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();
final String typeId = typeId_example; // String | 
final int laborFactor = 56; // int | 

try {
    final response = api.setBudgetConstLabor(typeId, laborFactor);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->setBudgetConstLabor: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **typeId** | **String**|  | 
 **laborFactor** | **int**|  | [optional] 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setBudgetConstMaterial**
> bool setBudgetConstMaterial(typeId, materialFactor)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();
final String typeId = typeId_example; // String | 
final int materialFactor = 56; // int | 

try {
    final response = api.setBudgetConstMaterial(typeId, materialFactor);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->setBudgetConstMaterial: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **typeId** | **String**|  | 
 **materialFactor** | **int**|  | [optional] 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setBudgetFactor**
> bool setBudgetFactor(budgetTypeId, key, attributeId, factor)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();
final String budgetTypeId = budgetTypeId_example; // String | 
final String key = key_example; // String | 
final String attributeId = attributeId_example; // String | 
final int factor = 56; // int | 

try {
    final response = api.setBudgetFactor(budgetTypeId, key, attributeId, factor);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->setBudgetFactor: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **budgetTypeId** | **String**|  | 
 **key** | **String**|  | 
 **attributeId** | **String**|  | 
 **factor** | **int**|  | [optional] 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setRoomConstLabor**
> bool setRoomConstLabor(budgetTypeId, roomId, laborFactor)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();
final String budgetTypeId = budgetTypeId_example; // String | 
final String roomId = roomId_example; // String | 
final int laborFactor = 56; // int | 

try {
    final response = api.setRoomConstLabor(budgetTypeId, roomId, laborFactor);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->setRoomConstLabor: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **budgetTypeId** | **String**|  | 
 **roomId** | **String**|  | 
 **laborFactor** | **int**|  | [optional] 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setRoomConstMaterial**
> bool setRoomConstMaterial(budgetTypeId, roomId, materialFactor)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetPlanningApi();
final String budgetTypeId = budgetTypeId_example; // String | 
final String roomId = roomId_example; // String | 
final int materialFactor = 56; // int | 

try {
    final response = api.setRoomConstMaterial(budgetTypeId, roomId, materialFactor);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetPlanningApi->setRoomConstMaterial: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **budgetTypeId** | **String**|  | 
 **roomId** | **String**|  | 
 **materialFactor** | **int**|  | [optional] 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

