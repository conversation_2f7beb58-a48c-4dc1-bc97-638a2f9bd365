# openapi.api.SmsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**callGet**](SmsApi.md#callget) | **GET** /sms/get | 
[**login**](SmsApi.md#login) | **POST** /sms/login | 短信登录
[**sendCode**](SmsApi.md#sendcode) | **POST** /sms/send | 发送验证码


# **callGet**
> callGet()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getSmsApi();

try {
    api.callGet();
} catch on DioException (e) {
    print('Exception when calling SmsApi->callGet: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **login**
> LoginResponse login(smsVerifyRequest)

短信登录

使用手机号和验证码登录并获取JWT令牌

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getSmsApi();
final SmsVerifyRequest smsVerifyRequest = ; // SmsVerifyRequest | 

try {
    final response = api.login(smsVerifyRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SmsApi->login: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **smsVerifyRequest** | [**SmsVerifyRequest**](SmsVerifyRequest.md)|  | 

### Return type

[**LoginResponse**](LoginResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **sendCode**
> SendCodeResponse sendCode(smsRequest)

发送验证码

向指定手机号发送登录验证码

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getSmsApi();
final SmsRequest smsRequest = ; // SmsRequest | 

try {
    final response = api.sendCode(smsRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SmsApi->sendCode: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **smsRequest** | [**SmsRequest**](SmsRequest.md)|  | 

### Return type

[**SendCodeResponse**](SendCodeResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

