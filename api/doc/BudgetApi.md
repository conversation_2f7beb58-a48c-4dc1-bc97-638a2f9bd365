# openapi.api.BudgetApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createBudget**](BudgetApi.md#createbudget) | **PUT** /budget/create | 创建预算管理清单
[**deleteBudget**](BudgetApi.md#deletebudget) | **DELETE** /budget/deleteBudget | 删除预算清单
[**deleteItem**](BudgetApi.md#deleteitem) | **DELETE** /budget/deleteItem | 删除预算项
[**getBudgetItems**](BudgetApi.md#getbudgetitems) | **GET** /budget/getBudgetItems | 获取预算项, 将payment_record转换为&#x60;BudgetPayment&#x60;
[**getBudgetsByUser**](BudgetApi.md#getbudgetsbyuser) | **GET** /budget/getBudget | 获取预算清单
[**getByProjectID**](BudgetApi.md#getbyprojectid) | **GET** /budget/getByProjectID | 
[**getDefaultBudget**](BudgetApi.md#getdefaultbudget) | **GET** /budget/defaultBudget | 
[**linkBudgetPayment**](BudgetApi.md#linkbudgetpayment) | **GET** /budget | 生成客户端BudgetPayment原型，无实际作用
[**parseImage**](BudgetApi.md#parseimage) | **POST** /budget/parseBudgetItemImage | 解析图片信息
[**parseShareURL**](BudgetApi.md#parseshareurl) | **PUT** /budget/parseShareURL | 解析分享链接
[**upsertItem**](BudgetApi.md#upsertitem) | **PUT** /budget/upsertItem | 创建预算项


# **createBudget**
> String createBudget(createBudgetRequest)

创建预算管理清单

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final CreateBudgetRequest createBudgetRequest = ; // CreateBudgetRequest | 

try {
    final response = api.createBudget(createBudgetRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->createBudget: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createBudgetRequest** | [**CreateBudgetRequest**](CreateBudgetRequest.md)|  | 

### Return type

**String**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteBudget**
> bool deleteBudget(id)

删除预算清单

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final String id = id_example; // String | 

try {
    final response = api.deleteBudget(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->deleteBudget: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteItem**
> bool deleteItem(ids)

删除预算项

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final BuiltList<String> ids = ; // BuiltList<String> | 

try {
    final response = api.deleteItem(ids);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->deleteItem: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ids** | [**BuiltList&lt;String&gt;**](String.md)|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getBudgetItems**
> BuiltList<TbBudgetItem> getBudgetItems(budgetId)

获取预算项, 将payment_record转换为`BudgetPayment`

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final String budgetId = budgetId_example; // String | 

try {
    final response = api.getBudgetItems(budgetId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->getBudgetItems: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **budgetId** | **String**|  | 

### Return type

[**BuiltList&lt;TbBudgetItem&gt;**](TbBudgetItem.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getBudgetsByUser**
> BuiltList<TbBudget> getBudgetsByUser()

获取预算清单

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();

try {
    final response = api.getBudgetsByUser();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->getBudgetsByUser: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbBudget&gt;**](TbBudget.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getByProjectID**
> TbBudget getByProjectID(projectId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final String projectId = projectId_example; // String | 

try {
    final response = api.getByProjectID(projectId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->getByProjectID: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | 

### Return type

[**TbBudget**](TbBudget.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getDefaultBudget**
> TbBudget getDefaultBudget()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();

try {
    final response = api.getDefaultBudget();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->getDefaultBudget: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**TbBudget**](TbBudget.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **linkBudgetPayment**
> BudgetPayment linkBudgetPayment()

生成客户端BudgetPayment原型，无实际作用

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();

try {
    final response = api.linkBudgetPayment();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->linkBudgetPayment: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BudgetPayment**](BudgetPayment.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **parseImage**
> BudgetItemParseResult parseImage(file)

解析图片信息

从图片中解析出商品信息

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final MultipartFile file = BINARY_DATA_HERE; // MultipartFile | 

try {
    final response = api.parseImage(file);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->parseImage: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **file** | **MultipartFile**|  | 

### Return type

[**BudgetItemParseResult**](BudgetItemParseResult.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **parseShareURL**
> BudgetItemParseResult parseShareURL(body)

解析分享链接

从分享链接中解析出商品信息

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final String body = body_example; // String | 

try {
    final response = api.parseShareURL(body);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->parseShareURL: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | **String**|  | 

### Return type

[**BudgetItemParseResult**](BudgetItemParseResult.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upsertItem**
> bool upsertItem(tbBudgetItem)

创建预算项

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetApi();
final BuiltList<TbBudgetItem> tbBudgetItem = ; // BuiltList<TbBudgetItem> | 

try {
    final response = api.upsertItem(tbBudgetItem);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetApi->upsertItem: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **tbBudgetItem** | [**BuiltList&lt;TbBudgetItem&gt;**](TbBudgetItem.md)|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

