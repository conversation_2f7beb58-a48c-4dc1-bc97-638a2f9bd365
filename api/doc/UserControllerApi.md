# openapi.api.UserControllerApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getCurrentUser**](UserControllerApi.md#getcurrentuser) | **GET** /user/getCurrentUser | 
[**getUsers1**](UserControllerApi.md#getusers1) | **GET** /user/getUsers | 


# **getCurrentUser**
> TbUser getCurrentUser()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getUserControllerApi();

try {
    final response = api.getCurrentUser();
    print(response);
} catch on DioException (e) {
    print('Exception when calling UserControllerApi->getCurrentUser: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**TbUser**](TbUser.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getUsers1**
> BuiltList<TbUser> getUsers1(userIds)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getUserControllerApi();
final BuiltList<String> userIds = ; // BuiltList<String> | 

try {
    final response = api.getUsers1(userIds);
    print(response);
} catch on DioException (e) {
    print('Exception when calling UserControllerApi->getUsers1: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **userIds** | [**BuiltList&lt;String&gt;**](String.md)|  | 

### Return type

[**BuiltList&lt;TbUser&gt;**](TbUser.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

