# openapi.api.PlanApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**deletePlan**](PlanApi.md#deleteplan) | **DELETE** /plan/delete | 
[**getPlan**](PlanApi.md#getplan) | **GET** /plan/getPlan | 
[**upsertPlan**](PlanApi.md#upsertplan) | **PUT** /plan/upsert | 


# **deletePlan**
> bool deletePlan(id)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getPlanApi();
final String id = id_example; // String | 

try {
    final response = api.deletePlan(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling PlanApi->deletePlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getPlan**
> BuiltList<TbPlanItem> getPlan(projectId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getPlanApi();
final String projectId = projectId_example; // String | 

try {
    final response = api.getPlan(projectId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling PlanApi->getPlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | 

### Return type

[**BuiltList&lt;TbPlanItem&gt;**](TbPlanItem.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upsertPlan**
> bool upsertPlan(projectId, tbPlanItem)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getPlanApi();
final String projectId = projectId_example; // String | 
final TbPlanItem tbPlanItem = ; // TbPlanItem | 

try {
    final response = api.upsertPlan(projectId, tbPlanItem);
    print(response);
} catch on DioException (e) {
    print('Exception when calling PlanApi->upsertPlan: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | 
 **tbPlanItem** | [**TbPlanItem**](TbPlanItem.md)|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

