# openapi.api.ProjectApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createRelation**](ProjectApi.md#createrelation) | **POST** /project/createRelation | 
[**deleteProject**](ProjectApi.md#deleteproject) | **DELETE** /project/deleteProject | 
[**deleteRelation**](ProjectApi.md#deleterelation) | **DELETE** /project/deleteRelation | 
[**deleteRole**](ProjectApi.md#deleterole) | **DELETE** /project/deleteRole | 
[**getMembers**](ProjectApi.md#getmembers) | **GET** /project/members | 
[**getProject**](ProjectApi.md#getproject) | **GET** /project/getProject | 
[**getProjects**](ProjectApi.md#getprojects) | **GET** /project/getProjects | 
[**getRoles**](ProjectApi.md#getroles) | **GET** /project/roles | 
[**upsertProject**](ProjectApi.md#upsertproject) | **PUT** /project/upsertProject | 
[**upsertRole**](ProjectApi.md#upsertrole) | **POST** /project/upsertRole | 


# **createRelation**
> bool createRelation(createRelation)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final CreateRelation createRelation = ; // CreateRelation | 

try {
    final response = api.createRelation(createRelation);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->createRelation: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createRelation** | [**CreateRelation**](CreateRelation.md)|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteProject**
> bool deleteProject(projectId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final String projectId = projectId_example; // String | 

try {
    final response = api.deleteProject(projectId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->deleteProject: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteRelation**
> bool deleteRelation(projectId, userId, roleId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final String projectId = projectId_example; // String | 
final String userId = userId_example; // String | 
final String roleId = roleId_example; // String | 

try {
    final response = api.deleteRelation(projectId, userId, roleId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->deleteRelation: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | 
 **userId** | **String**|  | 
 **roleId** | **String**|  | [optional] 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteRole**
> bool deleteRole(projectId, roleId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final String projectId = projectId_example; // String | 
final String roleId = roleId_example; // String | 

try {
    final response = api.deleteRole(projectId, roleId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->deleteRole: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | 
 **roleId** | **String**|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getMembers**
> BuiltList<RlUserProject> getMembers(projectId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final String projectId = projectId_example; // String | 

try {
    final response = api.getMembers(projectId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->getMembers: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | 

### Return type

[**BuiltList&lt;RlUserProject&gt;**](RlUserProject.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getProject**
> TbProject getProject(id)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final String id = id_example; // String | 

try {
    final response = api.getProject(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->getProject: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**TbProject**](TbProject.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getProjects**
> BuiltList<TbProject> getProjects()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();

try {
    final response = api.getProjects();
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->getProjects: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbProject&gt;**](TbProject.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getRoles**
> BuiltList<TbRole> getRoles(projectId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final String projectId = projectId_example; // String | 

try {
    final response = api.getRoles(projectId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->getRoles: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **projectId** | **String**|  | [optional] 

### Return type

[**BuiltList&lt;TbRole&gt;**](TbRole.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upsertProject**
> CreateResponseString upsertProject(upsertProjectRequest)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final UpsertProjectRequest upsertProjectRequest = ; // UpsertProjectRequest | 

try {
    final response = api.upsertProject(upsertProjectRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->upsertProject: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **upsertProjectRequest** | [**UpsertProjectRequest**](UpsertProjectRequest.md)|  | 

### Return type

[**CreateResponseString**](CreateResponseString.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upsertRole**
> bool upsertRole(tbRole)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getProjectApi();
final TbRole tbRole = ; // TbRole | 

try {
    final response = api.upsertRole(tbRole);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProjectApi->upsertRole: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **tbRole** | [**TbRole**](TbRole.md)|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

