# openapi.model.TbProject

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** |  | 
**name** | **String** |  | 
**description** | **String** |  | [optional] 
**address** | **String** |  | [optional] 
**latitude** | **double** |  | [optional] 
**longitude** | **double** |  | [optional] 
**area** | **double** |  | [optional] 
**style** | **String** |  | [optional] 
**bedrooms** | **int** |  | [optional] 
**livingrooms** | **int** |  | [optional] 
**bathrooms** | **int** |  | [optional] 
**kitchens** | **int** |  | [optional] 
**budget** | **int** |  | [optional] 
**startDate** | [**DateTime**](DateTime.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


