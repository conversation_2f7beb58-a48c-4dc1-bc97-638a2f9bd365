# openapi.model.TbHouseAttribute

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** |  | [optional] 
**type** | **String** |  | 
**name** | **String** |  | 
**index** | **int** |  | [optional] 
**defaultFactor** | **int** |  | [optional] 
**isDefault** | **bool** |  | [optional] 
**imageUrl** | **String** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


