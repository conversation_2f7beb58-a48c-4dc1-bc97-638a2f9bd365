# openapi.model.TbTask

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**title** | **String** |  | 
**assignToRole** | **String** |  | 
**createTime** | [**DateTime**](DateTime.md) |  | [optional] 
**targetTime** | [**DateTime**](DateTime.md) |  | [optional] 
**dueTime** | [**DateTime**](DateTime.md) |  | [optional] 
**groupId** | **String** |  | 
**createUser** | **String** |  | 
**planId** | **String** |  | [optional] 
**form** | [**JsonObject**](.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


