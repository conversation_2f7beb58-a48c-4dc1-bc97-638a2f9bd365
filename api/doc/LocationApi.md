# openapi.api.LocationApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**findLocationByAddress**](LocationApi.md#findlocationbyaddress) | **POST** /location | 
[**getLocationById**](LocationApi.md#getlocationbyid) | **GET** /location/getLocationById | 
[**getLocationByIp**](LocationApi.md#getlocationbyip) | **GET** /location/getLocationByIp | 
[**getRegionsByParentId**](LocationApi.md#getregionsbyparentid) | **GET** /location/getRegions | 


# **findLocationByAddress**
> BuiltList<Location> findLocationByAddress(body, city)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getLocationApi();
final String body = body_example; // String | 
final String city = city_example; // String | 

try {
    final response = api.findLocationByAddress(body, city);
    print(response);
} catch on DioException (e) {
    print('Exception when calling LocationApi->findLocationByAddress: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | **String**|  | 
 **city** | **String**|  | [optional] 

### Return type

[**BuiltList&lt;Location&gt;**](Location.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getLocationById**
> TbRegion getLocationById(id)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getLocationApi();
final String id = id_example; // String | 

try {
    final response = api.getLocationById(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling LocationApi->getLocationById: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**TbRegion**](TbRegion.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getLocationByIp**
> BuiltList<String> getLocationByIp()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getLocationApi();

try {
    final response = api.getLocationByIp();
    print(response);
} catch on DioException (e) {
    print('Exception when calling LocationApi->getLocationByIp: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**BuiltList&lt;String&gt;**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getRegionsByParentId**
> BuiltList<TbRegion> getRegionsByParentId(parentId)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getLocationApi();
final String parentId = parentId_example; // String | 

try {
    final response = api.getRegionsByParentId(parentId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling LocationApi->getRegionsByParentId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **parentId** | **String**|  | 

### Return type

[**BuiltList&lt;TbRegion&gt;**](TbRegion.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

