# openapi.api.AMapServiceControllerApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**syncRegion**](AMapServiceControllerApi.md#syncregion) | **POST** /amap/sync-region | 


# **syncRegion**
> syncRegion(body)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getAMapServiceControllerApi();
final String body = body_example; // String | 

try {
    api.syncRegion(body);
} catch on DioException (e) {
    print('Exception when calling AMapServiceControllerApi->syncRegion: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

