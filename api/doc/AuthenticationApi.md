# openapi.api.AuthenticationApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**anonymousLogin**](AuthenticationApi.md#anonymouslogin) | **POST** /auth/anonymousLogin | 
[**ping**](AuthenticationApi.md#ping) | **GET** /auth/ping | 
[**validateToken**](AuthenticationApi.md#validatetoken) | **GET** /auth/validate | 验证令牌


# **anonymousLogin**
> String anonymousLogin()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getAuthenticationApi();

try {
    final response = api.anonymousLogin();
    print(response);
} catch on DioException (e) {
    print('Exception when calling AuthenticationApi->anonymousLogin: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**String**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ping**
> String ping()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getAuthenticationApi();

try {
    final response = api.ping();
    print(response);
} catch on DioException (e) {
    print('Exception when calling AuthenticationApi->ping: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**String**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **validateToken**
> String validateToken(authorization)

验证令牌

验证JWT令牌是否有效并续期

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getAuthenticationApi();
final String authorization = authorization_example; // String | 

try {
    final response = api.validateToken(authorization);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AuthenticationApi->validateToken: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **authorization** | **String**|  | 

### Return type

**String**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

