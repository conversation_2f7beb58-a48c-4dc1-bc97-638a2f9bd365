# openapi.api.BasicApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getHouseAttributeTypes**](BasicApi.md#gethouseattributetypes) | **GET** /basic/getHouseAttributeTypes | 
[**getHouseAttributes**](BasicApi.md#gethouseattributes) | **GET** /basic/getHouseAttributes | 
[**getRoomTypes**](BasicApi.md#getroomtypes) | **GET** /basic/getRoomTypes | 


# **getHouseAttributeTypes**
> BuiltList<TbHouseAttributeType> getHouseAttributeTypes()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBasicApi();

try {
    final response = api.getHouseAttributeTypes();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BasicApi->getHouseAttributeTypes: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbHouseAttributeType&gt;**](TbHouseAttributeType.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getHouseAttributes**
> BuiltList<TbHouseAttribute> getHouseAttributes()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBasicApi();

try {
    final response = api.getHouseAttributes();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BasicApi->getHouseAttributes: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbHouseAttribute&gt;**](TbHouseAttribute.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getRoomTypes**
> BuiltList<TbRoomType> getRoomTypes()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBasicApi();

try {
    final response = api.getRoomTypes();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BasicApi->getRoomTypes: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbRoomType&gt;**](TbRoomType.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

