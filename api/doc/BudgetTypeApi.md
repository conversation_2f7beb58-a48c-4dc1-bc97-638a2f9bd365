# openapi.api.BudgetTypeApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://127.0.0.1:8080*

Method | HTTP request | Description
------------- | ------------- | -------------
[**deleteBudgetType**](BudgetTypeApi.md#deletebudgettype) | **DELETE** /budget-type/deleteBudgetItem | 删除预算项类型
[**getBudgetTypes**](BudgetTypeApi.md#getbudgettypes) | **GET** /budget-type/getBudgetTypes | 
[**upsertBudgetType**](BudgetTypeApi.md#upsertbudgettype) | **POST** /budget-type/upsertBudgetType | 


# **deleteBudgetType**
> bool deleteBudgetType(id)

删除预算项类型

### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetTypeApi();
final String id = id_example; // String | 

try {
    final response = api.deleteBudgetType(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetTypeApi->deleteBudgetType: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getBudgetTypes**
> BuiltList<TbBudgetType> getBudgetTypes()



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetTypeApi();

try {
    final response = api.getBudgetTypes();
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetTypeApi->getBudgetTypes: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BuiltList&lt;TbBudgetType&gt;**](TbBudgetType.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upsertBudgetType**
> bool upsertBudgetType(upsertBudgetTypeRequest)



### Example
```dart
import 'package:openapi/api.dart';

final api = Openapi().getBudgetTypeApi();
final UpsertBudgetTypeRequest upsertBudgetTypeRequest = ; // UpsertBudgetTypeRequest | 

try {
    final response = api.upsertBudgetType(upsertBudgetTypeRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BudgetTypeApi->upsertBudgetType: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **upsertBudgetTypeRequest** | [**UpsertBudgetTypeRequest**](UpsertBudgetTypeRequest.md)|  | 

### Return type

**bool**

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

