# openapi (EXPERIMENTAL)
Renovation项目API文档

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: v1.0.0
- Generator version: 7.14.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  openapi: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  openapi:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  openapi:
    path: /path/to/openapi
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:openapi/openapi.dart';


final api = Openapi().getAMapServiceControllerApi();
final String body = body_example; // String | 

try {
    api.syncRegion(body);
} catch on DioException (e) {
    print("Exception when calling AMapServiceControllerApi->syncRegion: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *http://127.0.0.1:8080*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*AMapServiceControllerApi*](doc/AMapServiceControllerApi.md) | [**syncRegion**](doc/AMapServiceControllerApi.md#syncregion) | **POST** /amap/sync-region | 
[*AuthenticationApi*](doc/AuthenticationApi.md) | [**anonymousLogin**](doc/AuthenticationApi.md#anonymouslogin) | **POST** /auth/anonymousLogin | 
[*AuthenticationApi*](doc/AuthenticationApi.md) | [**ping**](doc/AuthenticationApi.md#ping) | **GET** /auth/ping | 
[*AuthenticationApi*](doc/AuthenticationApi.md) | [**validateToken**](doc/AuthenticationApi.md#validatetoken) | **GET** /auth/validate | 验证令牌
[*BasicApi*](doc/BasicApi.md) | [**getHouseAttributeTypes**](doc/BasicApi.md#gethouseattributetypes) | **GET** /basic/getHouseAttributeTypes | 
[*BasicApi*](doc/BasicApi.md) | [**getHouseAttributes**](doc/BasicApi.md#gethouseattributes) | **GET** /basic/getHouseAttributes | 
[*BasicApi*](doc/BasicApi.md) | [**getRoomTypes**](doc/BasicApi.md#getroomtypes) | **GET** /basic/getRoomTypes | 
[*BudgetApi*](doc/BudgetApi.md) | [**createBudget**](doc/BudgetApi.md#createbudget) | **PUT** /budget/create | 创建预算管理清单
[*BudgetApi*](doc/BudgetApi.md) | [**deleteBudget**](doc/BudgetApi.md#deletebudget) | **DELETE** /budget/deleteBudget | 删除预算清单
[*BudgetApi*](doc/BudgetApi.md) | [**deleteItem**](doc/BudgetApi.md#deleteitem) | **DELETE** /budget/deleteItem | 删除预算项
[*BudgetApi*](doc/BudgetApi.md) | [**getBudgetItems**](doc/BudgetApi.md#getbudgetitems) | **GET** /budget/getBudgetItems | 获取预算项, 将payment_record转换为&#x60;BudgetPayment&#x60;
[*BudgetApi*](doc/BudgetApi.md) | [**getBudgetsByUser**](doc/BudgetApi.md#getbudgetsbyuser) | **GET** /budget/getBudget | 获取预算清单
[*BudgetApi*](doc/BudgetApi.md) | [**getByProjectID**](doc/BudgetApi.md#getbyprojectid) | **GET** /budget/getByProjectID | 
[*BudgetApi*](doc/BudgetApi.md) | [**getDefaultBudget**](doc/BudgetApi.md#getdefaultbudget) | **GET** /budget/defaultBudget | 
[*BudgetApi*](doc/BudgetApi.md) | [**linkBudgetPayment**](doc/BudgetApi.md#linkbudgetpayment) | **GET** /budget | 生成客户端BudgetPayment原型，无实际作用
[*BudgetApi*](doc/BudgetApi.md) | [**parseImage**](doc/BudgetApi.md#parseimage) | **POST** /budget/parseBudgetItemImage | 解析图片信息
[*BudgetApi*](doc/BudgetApi.md) | [**parseShareURL**](doc/BudgetApi.md#parseshareurl) | **PUT** /budget/parseShareURL | 解析分享链接
[*BudgetApi*](doc/BudgetApi.md) | [**upsertItem**](doc/BudgetApi.md#upsertitem) | **PUT** /budget/upsertItem | 创建预算项
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**budgetPlanningItem**](doc/BudgetPlanningApi.md#budgetplanningitem) | **GET** /budget-planning/_budgetPlanningItem | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**getBudgetConst**](doc/BudgetPlanningApi.md#getbudgetconst) | **GET** /budget-planning/getBudgetConst | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**getBudgetFactors**](doc/BudgetPlanningApi.md#getbudgetfactors) | **GET** /budget-planning/getBudgetFactors | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**getRoomConst**](doc/BudgetPlanningApi.md#getroomconst) | **GET** /budget-planning/getRoomConst | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**plan**](doc/BudgetPlanningApi.md#plan) | **POST** /budget-planning/plan | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**setBudgetConstFixedDeviceFactor**](doc/BudgetPlanningApi.md#setbudgetconstfixeddevicefactor) | **POST** /budget-planning/setBudgetConstFixedDevice | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**setBudgetConstLabor**](doc/BudgetPlanningApi.md#setbudgetconstlabor) | **POST** /budget-planning/setBudgetConstLabor | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**setBudgetConstMaterial**](doc/BudgetPlanningApi.md#setbudgetconstmaterial) | **POST** /budget-planning/setBudgetConstMaterial | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**setBudgetFactor**](doc/BudgetPlanningApi.md#setbudgetfactor) | **POST** /budget-planning/setBudgetFactor | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**setRoomConstLabor**](doc/BudgetPlanningApi.md#setroomconstlabor) | **POST** /budget-planning/setRoomConstLabor | 
[*BudgetPlanningApi*](doc/BudgetPlanningApi.md) | [**setRoomConstMaterial**](doc/BudgetPlanningApi.md#setroomconstmaterial) | **POST** /budget-planning/setRoomConstMaterial | 
[*BudgetTypeApi*](doc/BudgetTypeApi.md) | [**deleteBudgetType**](doc/BudgetTypeApi.md#deletebudgettype) | **DELETE** /budget-type/deleteBudgetItem | 删除预算项类型
[*BudgetTypeApi*](doc/BudgetTypeApi.md) | [**getBudgetTypes**](doc/BudgetTypeApi.md#getbudgettypes) | **GET** /budget-type/getBudgetTypes | 
[*BudgetTypeApi*](doc/BudgetTypeApi.md) | [**upsertBudgetType**](doc/BudgetTypeApi.md#upsertbudgettype) | **POST** /budget-type/upsertBudgetType | 
[*ContactControllerApi*](doc/ContactControllerApi.md) | [**getUsers**](doc/ContactControllerApi.md#getusers) | **POST** /contact/get | 
[*LocationApi*](doc/LocationApi.md) | [**findLocationByAddress**](doc/LocationApi.md#findlocationbyaddress) | **POST** /location | 
[*LocationApi*](doc/LocationApi.md) | [**getLocationById**](doc/LocationApi.md#getlocationbyid) | **GET** /location/getLocationById | 
[*LocationApi*](doc/LocationApi.md) | [**getLocationByIp**](doc/LocationApi.md#getlocationbyip) | **GET** /location/getLocationByIp | 
[*LocationApi*](doc/LocationApi.md) | [**getRegionsByParentId**](doc/LocationApi.md#getregionsbyparentid) | **GET** /location/getRegions | 
[*PlanApi*](doc/PlanApi.md) | [**deletePlan**](doc/PlanApi.md#deleteplan) | **DELETE** /plan/delete | 
[*PlanApi*](doc/PlanApi.md) | [**getPlan**](doc/PlanApi.md#getplan) | **GET** /plan/getPlan | 
[*PlanApi*](doc/PlanApi.md) | [**upsertPlan**](doc/PlanApi.md#upsertplan) | **PUT** /plan/upsert | 
[*ProjectApi*](doc/ProjectApi.md) | [**createRelation**](doc/ProjectApi.md#createrelation) | **POST** /project/createRelation | 
[*ProjectApi*](doc/ProjectApi.md) | [**deleteProject**](doc/ProjectApi.md#deleteproject) | **DELETE** /project/deleteProject | 
[*ProjectApi*](doc/ProjectApi.md) | [**deleteRelation**](doc/ProjectApi.md#deleterelation) | **DELETE** /project/deleteRelation | 
[*ProjectApi*](doc/ProjectApi.md) | [**deleteRole**](doc/ProjectApi.md#deleterole) | **DELETE** /project/deleteRole | 
[*ProjectApi*](doc/ProjectApi.md) | [**getMembers**](doc/ProjectApi.md#getmembers) | **GET** /project/members | 
[*ProjectApi*](doc/ProjectApi.md) | [**getProject**](doc/ProjectApi.md#getproject) | **GET** /project/getProject | 
[*ProjectApi*](doc/ProjectApi.md) | [**getProjects**](doc/ProjectApi.md#getprojects) | **GET** /project/getProjects | 
[*ProjectApi*](doc/ProjectApi.md) | [**getRoles**](doc/ProjectApi.md#getroles) | **GET** /project/roles | 
[*ProjectApi*](doc/ProjectApi.md) | [**upsertProject**](doc/ProjectApi.md#upsertproject) | **PUT** /project/upsertProject | 
[*ProjectApi*](doc/ProjectApi.md) | [**upsertRole**](doc/ProjectApi.md#upsertrole) | **POST** /project/upsertRole | 
[*SmsApi*](doc/SmsApi.md) | [**callGet**](doc/SmsApi.md#callget) | **GET** /sms/get | 
[*SmsApi*](doc/SmsApi.md) | [**login**](doc/SmsApi.md#login) | **POST** /sms/login | 短信登录
[*SmsApi*](doc/SmsApi.md) | [**sendCode**](doc/SmsApi.md#sendcode) | **POST** /sms/send | 发送验证码
[*TaskApi*](doc/TaskApi.md) | [**getAssignedTasks**](doc/TaskApi.md#getassignedtasks) | **GET** /task/getAssignedTasks | 
[*TaskApi*](doc/TaskApi.md) | [**getReceivedTasks**](doc/TaskApi.md#getreceivedtasks) | **GET** /task/getReceivedTasks | 
[*TaskApi*](doc/TaskApi.md) | [**getTasksInPlan**](doc/TaskApi.md#gettasksinplan) | **GET** /task/getTasksInPlan | 
[*TaskApi*](doc/TaskApi.md) | [**upsertTask**](doc/TaskApi.md#upserttask) | **PUT** /task/upsertTask | 
[*UserControllerApi*](doc/UserControllerApi.md) | [**getCurrentUser**](doc/UserControllerApi.md#getcurrentuser) | **GET** /user/getCurrentUser | 
[*UserControllerApi*](doc/UserControllerApi.md) | [**getUsers1**](doc/UserControllerApi.md#getusers1) | **GET** /user/getUsers | 


## Documentation For Models

 - [BudgetItemParseResult](doc/BudgetItemParseResult.md)
 - [BudgetPayment](doc/BudgetPayment.md)
 - [BudgetPlanningItem](doc/BudgetPlanningItem.md)
 - [BudgetPlanningParameter](doc/BudgetPlanningParameter.md)
 - [CreateBudgetRequest](doc/CreateBudgetRequest.md)
 - [CreateRelation](doc/CreateRelation.md)
 - [CreateResponseString](doc/CreateResponseString.md)
 - [CreateTaskRequest](doc/CreateTaskRequest.md)
 - [Location](doc/Location.md)
 - [LoginResponse](doc/LoginResponse.md)
 - [RlUserProject](doc/RlUserProject.md)
 - [SendCodeResponse](doc/SendCodeResponse.md)
 - [SmsRequest](doc/SmsRequest.md)
 - [SmsVerifyRequest](doc/SmsVerifyRequest.md)
 - [TbBudget](doc/TbBudget.md)
 - [TbBudgetConst](doc/TbBudgetConst.md)
 - [TbBudgetFactor](doc/TbBudgetFactor.md)
 - [TbBudgetItem](doc/TbBudgetItem.md)
 - [TbBudgetRoomConst](doc/TbBudgetRoomConst.md)
 - [TbBudgetType](doc/TbBudgetType.md)
 - [TbHouseAttribute](doc/TbHouseAttribute.md)
 - [TbHouseAttributeType](doc/TbHouseAttributeType.md)
 - [TbPlanItem](doc/TbPlanItem.md)
 - [TbProject](doc/TbProject.md)
 - [TbRegion](doc/TbRegion.md)
 - [TbRole](doc/TbRole.md)
 - [TbRoomType](doc/TbRoomType.md)
 - [TbTask](doc/TbTask.md)
 - [TbUser](doc/TbUser.md)
 - [UpsertBudgetTypeRequest](doc/UpsertBudgetTypeRequest.md)
 - [UpsertProjectRequest](doc/UpsertProjectRequest.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### bearerAuth

- **Type**: HTTP Bearer Token authentication (JWT)


## Author



