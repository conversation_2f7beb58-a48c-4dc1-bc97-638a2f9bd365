//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/tb_project.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'upsert_project_request.g.dart';

/// UpsertProjectRequest
///
/// Properties:
/// * [project] 
/// * [creatorName] 
/// * [creatorRoles] 
@BuiltValue()
abstract class UpsertProjectRequest implements Built<UpsertProjectRequest, UpsertProjectRequestBuilder> {
  @BuiltValueField(wireName: r'project')
  TbProject get project;

  @BuiltValueField(wireName: r'creatorName')
  String get creatorName;

  @BuiltValueField(wireName: r'creatorRoles')
  BuiltList<String>? get creatorRoles;

  UpsertProjectRequest._();

  factory UpsertProjectRequest([void updates(UpsertProjectRequestBuilder b)]) = _$UpsertProjectRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpsertProjectRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpsertProjectRequest> get serializer => _$UpsertProjectRequestSerializer();
}

class _$UpsertProjectRequestSerializer implements PrimitiveSerializer<UpsertProjectRequest> {
  @override
  final Iterable<Type> types = const [UpsertProjectRequest, _$UpsertProjectRequest];

  @override
  final String wireName = r'UpsertProjectRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpsertProjectRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'project';
    yield serializers.serialize(
      object.project,
      specifiedType: const FullType(TbProject),
    );
    yield r'creatorName';
    yield serializers.serialize(
      object.creatorName,
      specifiedType: const FullType(String),
    );
    if (object.creatorRoles != null) {
      yield r'creatorRoles';
      yield serializers.serialize(
        object.creatorRoles,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpsertProjectRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpsertProjectRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'project':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(TbProject),
          ) as TbProject;
          result.project.replace(valueDes);
          break;
        case r'creatorName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.creatorName = valueDes;
          break;
        case r'creatorRoles':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.creatorRoles.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpsertProjectRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpsertProjectRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

