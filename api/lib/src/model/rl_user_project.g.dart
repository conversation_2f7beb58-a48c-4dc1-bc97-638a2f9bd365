// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rl_user_project.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RlUserProject extends RlUserProject {
  @override
  final String userId;
  @override
  final String projectId;
  @override
  final bool? favorite;
  @override
  final bool? disabled;
  @override
  final String? role;
  @override
  final String id;
  @override
  final String? userDisplayName;

  factory _$RlUserProject([void Function(RlUserProjectBuilder)? updates]) =>
      (RlUserProjectBuilder()..update(updates))._build();

  _$RlUserProject._(
      {required this.userId,
      required this.projectId,
      this.favorite,
      this.disabled,
      this.role,
      required this.id,
      this.userDisplayName})
      : super._();
  @override
  RlUserProject rebuild(void Function(RlUserProjectBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RlUserProjectBuilder toBuilder() => RlUserProjectBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RlUserProject &&
        userId == other.userId &&
        projectId == other.projectId &&
        favorite == other.favorite &&
        disabled == other.disabled &&
        role == other.role &&
        id == other.id &&
        userDisplayName == other.userDisplayName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, projectId.hashCode);
    _$hash = $jc(_$hash, favorite.hashCode);
    _$hash = $jc(_$hash, disabled.hashCode);
    _$hash = $jc(_$hash, role.hashCode);
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, userDisplayName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RlUserProject')
          ..add('userId', userId)
          ..add('projectId', projectId)
          ..add('favorite', favorite)
          ..add('disabled', disabled)
          ..add('role', role)
          ..add('id', id)
          ..add('userDisplayName', userDisplayName))
        .toString();
  }
}

class RlUserProjectBuilder
    implements Builder<RlUserProject, RlUserProjectBuilder> {
  _$RlUserProject? _$v;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  String? _projectId;
  String? get projectId => _$this._projectId;
  set projectId(String? projectId) => _$this._projectId = projectId;

  bool? _favorite;
  bool? get favorite => _$this._favorite;
  set favorite(bool? favorite) => _$this._favorite = favorite;

  bool? _disabled;
  bool? get disabled => _$this._disabled;
  set disabled(bool? disabled) => _$this._disabled = disabled;

  String? _role;
  String? get role => _$this._role;
  set role(String? role) => _$this._role = role;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _userDisplayName;
  String? get userDisplayName => _$this._userDisplayName;
  set userDisplayName(String? userDisplayName) =>
      _$this._userDisplayName = userDisplayName;

  RlUserProjectBuilder() {
    RlUserProject._defaults(this);
  }

  RlUserProjectBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _projectId = $v.projectId;
      _favorite = $v.favorite;
      _disabled = $v.disabled;
      _role = $v.role;
      _id = $v.id;
      _userDisplayName = $v.userDisplayName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RlUserProject other) {
    _$v = other as _$RlUserProject;
  }

  @override
  void update(void Function(RlUserProjectBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RlUserProject build() => _build();

  _$RlUserProject _build() {
    final _$result = _$v ??
        _$RlUserProject._(
          userId: BuiltValueNullFieldError.checkNotNull(
              userId, r'RlUserProject', 'userId'),
          projectId: BuiltValueNullFieldError.checkNotNull(
              projectId, r'RlUserProject', 'projectId'),
          favorite: favorite,
          disabled: disabled,
          role: role,
          id: BuiltValueNullFieldError.checkNotNull(id, r'RlUserProject', 'id'),
          userDisplayName: userDisplayName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
