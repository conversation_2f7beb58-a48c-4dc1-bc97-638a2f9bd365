//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'create_response_string.g.dart';

/// CreateResponseString
///
/// Properties:
/// * [success] 
/// * [id] 
@BuiltValue()
abstract class CreateResponseString implements Built<CreateResponseString, CreateResponseStringBuilder> {
  @BuiltValueField(wireName: r'success')
  bool get success;

  @BuiltValueField(wireName: r'id')
  String? get id;

  CreateResponseString._();

  factory CreateResponseString([void updates(CreateResponseStringBuilder b)]) = _$CreateResponseString;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CreateResponseStringBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CreateResponseString> get serializer => _$CreateResponseStringSerializer();
}

class _$CreateResponseStringSerializer implements PrimitiveSerializer<CreateResponseString> {
  @override
  final Iterable<Type> types = const [CreateResponseString, _$CreateResponseString];

  @override
  final String wireName = r'CreateResponseString';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CreateResponseString object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'success';
    yield serializers.serialize(
      object.success,
      specifiedType: const FullType(bool),
    );
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CreateResponseString object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CreateResponseStringBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CreateResponseString deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CreateResponseStringBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

