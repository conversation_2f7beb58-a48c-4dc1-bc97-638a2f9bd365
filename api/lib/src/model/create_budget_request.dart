//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'create_budget_request.g.dart';

/// CreateBudgetRequest
///
/// Properties:
/// * [name] 
/// * [projectId] 
@BuiltValue()
abstract class CreateBudgetRequest implements Built<CreateBudgetRequest, CreateBudgetRequestBuilder> {
  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'projectId')
  String? get projectId;

  CreateBudgetRequest._();

  factory CreateBudgetRequest([void updates(CreateBudgetRequestBuilder b)]) = _$CreateBudgetRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CreateBudgetRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CreateBudgetRequest> get serializer => _$CreateBudgetRequestSerializer();
}

class _$CreateBudgetRequestSerializer implements PrimitiveSerializer<CreateBudgetRequest> {
  @override
  final Iterable<Type> types = const [CreateBudgetRequest, _$CreateBudgetRequest];

  @override
  final String wireName = r'CreateBudgetRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CreateBudgetRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType(String),
      );
    }
    if (object.projectId != null) {
      yield r'projectId';
      yield serializers.serialize(
        object.projectId,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CreateBudgetRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CreateBudgetRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'projectId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.projectId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CreateBudgetRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CreateBudgetRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

