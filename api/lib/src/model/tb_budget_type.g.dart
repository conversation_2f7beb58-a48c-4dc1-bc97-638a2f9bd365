// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_budget_type.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbBudgetType extends TbBudgetType {
  @override
  final String? id;
  @override
  final String? parent;
  @override
  final String name;
  @override
  final int? index;
  @override
  final bool? planable;

  factory _$TbBudgetType([void Function(TbBudgetTypeBuilder)? updates]) =>
      (TbBudgetTypeBuilder()..update(updates))._build();

  _$TbBudgetType._(
      {this.id, this.parent, required this.name, this.index, this.planable})
      : super._();
  @override
  TbBudgetType rebuild(void Function(TbBudgetTypeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbBudgetTypeBuilder toBuilder() => TbBudgetTypeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbBudgetType &&
        id == other.id &&
        parent == other.parent &&
        name == other.name &&
        index == other.index &&
        planable == other.planable;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, parent.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, index.hashCode);
    _$hash = $jc(_$hash, planable.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbBudgetType')
          ..add('id', id)
          ..add('parent', parent)
          ..add('name', name)
          ..add('index', index)
          ..add('planable', planable))
        .toString();
  }
}

class TbBudgetTypeBuilder
    implements Builder<TbBudgetType, TbBudgetTypeBuilder> {
  _$TbBudgetType? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _parent;
  String? get parent => _$this._parent;
  set parent(String? parent) => _$this._parent = parent;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  int? _index;
  int? get index => _$this._index;
  set index(int? index) => _$this._index = index;

  bool? _planable;
  bool? get planable => _$this._planable;
  set planable(bool? planable) => _$this._planable = planable;

  TbBudgetTypeBuilder() {
    TbBudgetType._defaults(this);
  }

  TbBudgetTypeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _parent = $v.parent;
      _name = $v.name;
      _index = $v.index;
      _planable = $v.planable;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbBudgetType other) {
    _$v = other as _$TbBudgetType;
  }

  @override
  void update(void Function(TbBudgetTypeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbBudgetType build() => _build();

  _$TbBudgetType _build() {
    final _$result = _$v ??
        _$TbBudgetType._(
          id: id,
          parent: parent,
          name: BuiltValueNullFieldError.checkNotNull(
              name, r'TbBudgetType', 'name'),
          index: index,
          planable: planable,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
