// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget_item_parse_result.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BudgetItemParseResult extends BudgetItemParseResult {
  @override
  final String? title;
  @override
  final double? lineAmount;
  @override
  final String? url;
  @override
  final String? platform;

  factory _$BudgetItemParseResult(
          [void Function(BudgetItemParseResultBuilder)? updates]) =>
      (BudgetItemParseResultBuilder()..update(updates))._build();

  _$BudgetItemParseResult._(
      {this.title, this.lineAmount, this.url, this.platform})
      : super._();
  @override
  BudgetItemParseResult rebuild(
          void Function(BudgetItemParseResultBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BudgetItemParseResultBuilder toBuilder() =>
      BudgetItemParseResultBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BudgetItemParseResult &&
        title == other.title &&
        lineAmount == other.lineAmount &&
        url == other.url &&
        platform == other.platform;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, lineAmount.hashCode);
    _$hash = $jc(_$hash, url.hashCode);
    _$hash = $jc(_$hash, platform.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BudgetItemParseResult')
          ..add('title', title)
          ..add('lineAmount', lineAmount)
          ..add('url', url)
          ..add('platform', platform))
        .toString();
  }
}

class BudgetItemParseResultBuilder
    implements Builder<BudgetItemParseResult, BudgetItemParseResultBuilder> {
  _$BudgetItemParseResult? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  double? _lineAmount;
  double? get lineAmount => _$this._lineAmount;
  set lineAmount(double? lineAmount) => _$this._lineAmount = lineAmount;

  String? _url;
  String? get url => _$this._url;
  set url(String? url) => _$this._url = url;

  String? _platform;
  String? get platform => _$this._platform;
  set platform(String? platform) => _$this._platform = platform;

  BudgetItemParseResultBuilder() {
    BudgetItemParseResult._defaults(this);
  }

  BudgetItemParseResultBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _lineAmount = $v.lineAmount;
      _url = $v.url;
      _platform = $v.platform;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BudgetItemParseResult other) {
    _$v = other as _$BudgetItemParseResult;
  }

  @override
  void update(void Function(BudgetItemParseResultBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BudgetItemParseResult build() => _build();

  _$BudgetItemParseResult _build() {
    final _$result = _$v ??
        _$BudgetItemParseResult._(
          title: title,
          lineAmount: lineAmount,
          url: url,
          platform: platform,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
