//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'upsert_budget_type_request.g.dart';

/// UpsertBudgetTypeRequest
///
/// Properties:
/// * [id] 
/// * [name] 
/// * [parentId] 
/// * [index] 
@BuiltValue()
abstract class UpsertBudgetTypeRequest implements Built<UpsertBudgetTypeRequest, UpsertBudgetTypeRequestBuilder> {
  @BuiltValueField(wireName: r'id')
  String get id;

  @BuiltValueField(wireName: r'name')
  String get name;

  @BuiltValueField(wireName: r'parentId')
  String? get parentId;

  @BuiltValueField(wireName: r'index')
  int get index;

  UpsertBudgetTypeRequest._();

  factory UpsertBudgetTypeRequest([void updates(UpsertBudgetTypeRequestBuilder b)]) = _$UpsertBudgetTypeRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpsertBudgetTypeRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpsertBudgetTypeRequest> get serializer => _$UpsertBudgetTypeRequestSerializer();
}

class _$UpsertBudgetTypeRequestSerializer implements PrimitiveSerializer<UpsertBudgetTypeRequest> {
  @override
  final Iterable<Type> types = const [UpsertBudgetTypeRequest, _$UpsertBudgetTypeRequest];

  @override
  final String wireName = r'UpsertBudgetTypeRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpsertBudgetTypeRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(String),
    );
    yield r'name';
    yield serializers.serialize(
      object.name,
      specifiedType: const FullType(String),
    );
    if (object.parentId != null) {
      yield r'parentId';
      yield serializers.serialize(
        object.parentId,
        specifiedType: const FullType(String),
      );
    }
    yield r'index';
    yield serializers.serialize(
      object.index,
      specifiedType: const FullType(int),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    UpsertBudgetTypeRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpsertBudgetTypeRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'parentId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.parentId = valueDes;
          break;
        case r'index':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.index = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpsertBudgetTypeRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpsertBudgetTypeRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

