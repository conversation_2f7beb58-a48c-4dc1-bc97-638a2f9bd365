// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget_planning_item.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BudgetPlanningItem extends BudgetPlanningItem {
  @override
  final String name;
  @override
  final String type;
  @override
  final double? budget;
  @override
  final int? count;
  @override
  final BuiltList<BudgetPlanningItem>? children;

  factory _$BudgetPlanningItem(
          [void Function(BudgetPlanningItemBuilder)? updates]) =>
      (BudgetPlanningItemBuilder()..update(updates))._build();

  _$BudgetPlanningItem._(
      {required this.name,
      required this.type,
      this.budget,
      this.count,
      this.children})
      : super._();
  @override
  BudgetPlanningItem rebuild(
          void Function(BudgetPlanningItemBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BudgetPlanningItemBuilder toBuilder() =>
      BudgetPlanningItemBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BudgetPlanningItem &&
        name == other.name &&
        type == other.type &&
        budget == other.budget &&
        count == other.count &&
        children == other.children;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, budget.hashCode);
    _$hash = $jc(_$hash, count.hashCode);
    _$hash = $jc(_$hash, children.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BudgetPlanningItem')
          ..add('name', name)
          ..add('type', type)
          ..add('budget', budget)
          ..add('count', count)
          ..add('children', children))
        .toString();
  }
}

class BudgetPlanningItemBuilder
    implements Builder<BudgetPlanningItem, BudgetPlanningItemBuilder> {
  _$BudgetPlanningItem? _$v;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _type;
  String? get type => _$this._type;
  set type(String? type) => _$this._type = type;

  double? _budget;
  double? get budget => _$this._budget;
  set budget(double? budget) => _$this._budget = budget;

  int? _count;
  int? get count => _$this._count;
  set count(int? count) => _$this._count = count;

  ListBuilder<BudgetPlanningItem>? _children;
  ListBuilder<BudgetPlanningItem> get children =>
      _$this._children ??= ListBuilder<BudgetPlanningItem>();
  set children(ListBuilder<BudgetPlanningItem>? children) =>
      _$this._children = children;

  BudgetPlanningItemBuilder() {
    BudgetPlanningItem._defaults(this);
  }

  BudgetPlanningItemBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _name = $v.name;
      _type = $v.type;
      _budget = $v.budget;
      _count = $v.count;
      _children = $v.children?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BudgetPlanningItem other) {
    _$v = other as _$BudgetPlanningItem;
  }

  @override
  void update(void Function(BudgetPlanningItemBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BudgetPlanningItem build() => _build();

  _$BudgetPlanningItem _build() {
    _$BudgetPlanningItem _$result;
    try {
      _$result = _$v ??
          _$BudgetPlanningItem._(
            name: BuiltValueNullFieldError.checkNotNull(
                name, r'BudgetPlanningItem', 'name'),
            type: BuiltValueNullFieldError.checkNotNull(
                type, r'BudgetPlanningItem', 'type'),
            budget: budget,
            count: count,
            children: _children?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'children';
        _children?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BudgetPlanningItem', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
