// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget_payment.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BudgetPayment extends BudgetPayment {
  @override
  final String? title;
  @override
  final double amount;
  @override
  final DateTime time;

  factory _$BudgetPayment([void Function(BudgetPaymentBuilder)? updates]) =>
      (BudgetPaymentBuilder()..update(updates))._build();

  _$BudgetPayment._({this.title, required this.amount, required this.time})
      : super._();
  @override
  BudgetPayment rebuild(void Function(BudgetPaymentBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BudgetPaymentBuilder toBuilder() => BudgetPaymentBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BudgetPayment &&
        title == other.title &&
        amount == other.amount &&
        time == other.time;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, time.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BudgetPayment')
          ..add('title', title)
          ..add('amount', amount)
          ..add('time', time))
        .toString();
  }
}

class BudgetPaymentBuilder
    implements Builder<BudgetPayment, BudgetPaymentBuilder> {
  _$BudgetPayment? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  DateTime? _time;
  DateTime? get time => _$this._time;
  set time(DateTime? time) => _$this._time = time;

  BudgetPaymentBuilder() {
    BudgetPayment._defaults(this);
  }

  BudgetPaymentBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _amount = $v.amount;
      _time = $v.time;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BudgetPayment other) {
    _$v = other as _$BudgetPayment;
  }

  @override
  void update(void Function(BudgetPaymentBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BudgetPayment build() => _build();

  _$BudgetPayment _build() {
    final _$result = _$v ??
        _$BudgetPayment._(
          title: title,
          amount: BuiltValueNullFieldError.checkNotNull(
              amount, r'BudgetPayment', 'amount'),
          time: BuiltValueNullFieldError.checkNotNull(
              time, r'BudgetPayment', 'time'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
