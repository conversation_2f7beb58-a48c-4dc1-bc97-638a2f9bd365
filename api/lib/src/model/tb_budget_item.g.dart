// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_budget_item.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbBudgetItem extends TbBudgetItem {
  @override
  final String id;
  @override
  final String? parentId;
  @override
  final String budgetId;
  @override
  final String? title;
  @override
  final int? index;
  @override
  final String? description;
  @override
  final double? quantity;
  @override
  final double? unitPrice;
  @override
  final double lineAmount;
  @override
  final bool? checked;
  @override
  final String? url;
  @override
  final int? sizeHeight;
  @override
  final int? sizeWidth;
  @override
  final int? sizeDepth;
  @override
  final String? contactUserId;
  @override
  final String? contactName;
  @override
  final String? contactPhone;
  @override
  final String? model;
  @override
  final String? platform;
  @override
  final String type;
  @override
  final BuiltList<String>? paymentRecords;

  factory _$TbBudgetItem([void Function(TbBudgetItemBuilder)? updates]) =>
      (TbBudgetItemBuilder()..update(updates))._build();

  _$TbBudgetItem._(
      {required this.id,
      this.parentId,
      required this.budgetId,
      this.title,
      this.index,
      this.description,
      this.quantity,
      this.unitPrice,
      required this.lineAmount,
      this.checked,
      this.url,
      this.sizeHeight,
      this.sizeWidth,
      this.sizeDepth,
      this.contactUserId,
      this.contactName,
      this.contactPhone,
      this.model,
      this.platform,
      required this.type,
      this.paymentRecords})
      : super._();
  @override
  TbBudgetItem rebuild(void Function(TbBudgetItemBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbBudgetItemBuilder toBuilder() => TbBudgetItemBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbBudgetItem &&
        id == other.id &&
        parentId == other.parentId &&
        budgetId == other.budgetId &&
        title == other.title &&
        index == other.index &&
        description == other.description &&
        quantity == other.quantity &&
        unitPrice == other.unitPrice &&
        lineAmount == other.lineAmount &&
        checked == other.checked &&
        url == other.url &&
        sizeHeight == other.sizeHeight &&
        sizeWidth == other.sizeWidth &&
        sizeDepth == other.sizeDepth &&
        contactUserId == other.contactUserId &&
        contactName == other.contactName &&
        contactPhone == other.contactPhone &&
        model == other.model &&
        platform == other.platform &&
        type == other.type &&
        paymentRecords == other.paymentRecords;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, budgetId.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, index.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, quantity.hashCode);
    _$hash = $jc(_$hash, unitPrice.hashCode);
    _$hash = $jc(_$hash, lineAmount.hashCode);
    _$hash = $jc(_$hash, checked.hashCode);
    _$hash = $jc(_$hash, url.hashCode);
    _$hash = $jc(_$hash, sizeHeight.hashCode);
    _$hash = $jc(_$hash, sizeWidth.hashCode);
    _$hash = $jc(_$hash, sizeDepth.hashCode);
    _$hash = $jc(_$hash, contactUserId.hashCode);
    _$hash = $jc(_$hash, contactName.hashCode);
    _$hash = $jc(_$hash, contactPhone.hashCode);
    _$hash = $jc(_$hash, model.hashCode);
    _$hash = $jc(_$hash, platform.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, paymentRecords.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbBudgetItem')
          ..add('id', id)
          ..add('parentId', parentId)
          ..add('budgetId', budgetId)
          ..add('title', title)
          ..add('index', index)
          ..add('description', description)
          ..add('quantity', quantity)
          ..add('unitPrice', unitPrice)
          ..add('lineAmount', lineAmount)
          ..add('checked', checked)
          ..add('url', url)
          ..add('sizeHeight', sizeHeight)
          ..add('sizeWidth', sizeWidth)
          ..add('sizeDepth', sizeDepth)
          ..add('contactUserId', contactUserId)
          ..add('contactName', contactName)
          ..add('contactPhone', contactPhone)
          ..add('model', model)
          ..add('platform', platform)
          ..add('type', type)
          ..add('paymentRecords', paymentRecords))
        .toString();
  }
}

class TbBudgetItemBuilder
    implements Builder<TbBudgetItem, TbBudgetItemBuilder> {
  _$TbBudgetItem? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  String? _budgetId;
  String? get budgetId => _$this._budgetId;
  set budgetId(String? budgetId) => _$this._budgetId = budgetId;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  int? _index;
  int? get index => _$this._index;
  set index(int? index) => _$this._index = index;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  double? _quantity;
  double? get quantity => _$this._quantity;
  set quantity(double? quantity) => _$this._quantity = quantity;

  double? _unitPrice;
  double? get unitPrice => _$this._unitPrice;
  set unitPrice(double? unitPrice) => _$this._unitPrice = unitPrice;

  double? _lineAmount;
  double? get lineAmount => _$this._lineAmount;
  set lineAmount(double? lineAmount) => _$this._lineAmount = lineAmount;

  bool? _checked;
  bool? get checked => _$this._checked;
  set checked(bool? checked) => _$this._checked = checked;

  String? _url;
  String? get url => _$this._url;
  set url(String? url) => _$this._url = url;

  int? _sizeHeight;
  int? get sizeHeight => _$this._sizeHeight;
  set sizeHeight(int? sizeHeight) => _$this._sizeHeight = sizeHeight;

  int? _sizeWidth;
  int? get sizeWidth => _$this._sizeWidth;
  set sizeWidth(int? sizeWidth) => _$this._sizeWidth = sizeWidth;

  int? _sizeDepth;
  int? get sizeDepth => _$this._sizeDepth;
  set sizeDepth(int? sizeDepth) => _$this._sizeDepth = sizeDepth;

  String? _contactUserId;
  String? get contactUserId => _$this._contactUserId;
  set contactUserId(String? contactUserId) =>
      _$this._contactUserId = contactUserId;

  String? _contactName;
  String? get contactName => _$this._contactName;
  set contactName(String? contactName) => _$this._contactName = contactName;

  String? _contactPhone;
  String? get contactPhone => _$this._contactPhone;
  set contactPhone(String? contactPhone) => _$this._contactPhone = contactPhone;

  String? _model;
  String? get model => _$this._model;
  set model(String? model) => _$this._model = model;

  String? _platform;
  String? get platform => _$this._platform;
  set platform(String? platform) => _$this._platform = platform;

  String? _type;
  String? get type => _$this._type;
  set type(String? type) => _$this._type = type;

  ListBuilder<String>? _paymentRecords;
  ListBuilder<String> get paymentRecords =>
      _$this._paymentRecords ??= ListBuilder<String>();
  set paymentRecords(ListBuilder<String>? paymentRecords) =>
      _$this._paymentRecords = paymentRecords;

  TbBudgetItemBuilder() {
    TbBudgetItem._defaults(this);
  }

  TbBudgetItemBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _parentId = $v.parentId;
      _budgetId = $v.budgetId;
      _title = $v.title;
      _index = $v.index;
      _description = $v.description;
      _quantity = $v.quantity;
      _unitPrice = $v.unitPrice;
      _lineAmount = $v.lineAmount;
      _checked = $v.checked;
      _url = $v.url;
      _sizeHeight = $v.sizeHeight;
      _sizeWidth = $v.sizeWidth;
      _sizeDepth = $v.sizeDepth;
      _contactUserId = $v.contactUserId;
      _contactName = $v.contactName;
      _contactPhone = $v.contactPhone;
      _model = $v.model;
      _platform = $v.platform;
      _type = $v.type;
      _paymentRecords = $v.paymentRecords?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbBudgetItem other) {
    _$v = other as _$TbBudgetItem;
  }

  @override
  void update(void Function(TbBudgetItemBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbBudgetItem build() => _build();

  _$TbBudgetItem _build() {
    _$TbBudgetItem _$result;
    try {
      _$result = _$v ??
          _$TbBudgetItem._(
            id: BuiltValueNullFieldError.checkNotNull(
                id, r'TbBudgetItem', 'id'),
            parentId: parentId,
            budgetId: BuiltValueNullFieldError.checkNotNull(
                budgetId, r'TbBudgetItem', 'budgetId'),
            title: title,
            index: index,
            description: description,
            quantity: quantity,
            unitPrice: unitPrice,
            lineAmount: BuiltValueNullFieldError.checkNotNull(
                lineAmount, r'TbBudgetItem', 'lineAmount'),
            checked: checked,
            url: url,
            sizeHeight: sizeHeight,
            sizeWidth: sizeWidth,
            sizeDepth: sizeDepth,
            contactUserId: contactUserId,
            contactName: contactName,
            contactPhone: contactPhone,
            model: model,
            platform: platform,
            type: BuiltValueNullFieldError.checkNotNull(
                type, r'TbBudgetItem', 'type'),
            paymentRecords: _paymentRecords?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'paymentRecords';
        _paymentRecords?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TbBudgetItem', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
