// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_project.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbProject extends TbProject {
  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? address;
  @override
  final double? latitude;
  @override
  final double? longitude;
  @override
  final double? area;
  @override
  final String? style;
  @override
  final int? bedrooms;
  @override
  final int? livingrooms;
  @override
  final int? bathrooms;
  @override
  final int? kitchens;
  @override
  final int? budget;
  @override
  final DateTime? startDate;

  factory _$TbProject([void Function(TbProjectBuilder)? updates]) =>
      (TbProjectBuilder()..update(updates))._build();

  _$TbProject._(
      {required this.id,
      required this.name,
      this.description,
      this.address,
      this.latitude,
      this.longitude,
      this.area,
      this.style,
      this.bedrooms,
      this.livingrooms,
      this.bathrooms,
      this.kitchens,
      this.budget,
      this.startDate})
      : super._();
  @override
  TbProject rebuild(void Function(TbProjectBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbProjectBuilder toBuilder() => TbProjectBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbProject &&
        id == other.id &&
        name == other.name &&
        description == other.description &&
        address == other.address &&
        latitude == other.latitude &&
        longitude == other.longitude &&
        area == other.area &&
        style == other.style &&
        bedrooms == other.bedrooms &&
        livingrooms == other.livingrooms &&
        bathrooms == other.bathrooms &&
        kitchens == other.kitchens &&
        budget == other.budget &&
        startDate == other.startDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, latitude.hashCode);
    _$hash = $jc(_$hash, longitude.hashCode);
    _$hash = $jc(_$hash, area.hashCode);
    _$hash = $jc(_$hash, style.hashCode);
    _$hash = $jc(_$hash, bedrooms.hashCode);
    _$hash = $jc(_$hash, livingrooms.hashCode);
    _$hash = $jc(_$hash, bathrooms.hashCode);
    _$hash = $jc(_$hash, kitchens.hashCode);
    _$hash = $jc(_$hash, budget.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbProject')
          ..add('id', id)
          ..add('name', name)
          ..add('description', description)
          ..add('address', address)
          ..add('latitude', latitude)
          ..add('longitude', longitude)
          ..add('area', area)
          ..add('style', style)
          ..add('bedrooms', bedrooms)
          ..add('livingrooms', livingrooms)
          ..add('bathrooms', bathrooms)
          ..add('kitchens', kitchens)
          ..add('budget', budget)
          ..add('startDate', startDate))
        .toString();
  }
}

class TbProjectBuilder implements Builder<TbProject, TbProjectBuilder> {
  _$TbProject? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  double? _latitude;
  double? get latitude => _$this._latitude;
  set latitude(double? latitude) => _$this._latitude = latitude;

  double? _longitude;
  double? get longitude => _$this._longitude;
  set longitude(double? longitude) => _$this._longitude = longitude;

  double? _area;
  double? get area => _$this._area;
  set area(double? area) => _$this._area = area;

  String? _style;
  String? get style => _$this._style;
  set style(String? style) => _$this._style = style;

  int? _bedrooms;
  int? get bedrooms => _$this._bedrooms;
  set bedrooms(int? bedrooms) => _$this._bedrooms = bedrooms;

  int? _livingrooms;
  int? get livingrooms => _$this._livingrooms;
  set livingrooms(int? livingrooms) => _$this._livingrooms = livingrooms;

  int? _bathrooms;
  int? get bathrooms => _$this._bathrooms;
  set bathrooms(int? bathrooms) => _$this._bathrooms = bathrooms;

  int? _kitchens;
  int? get kitchens => _$this._kitchens;
  set kitchens(int? kitchens) => _$this._kitchens = kitchens;

  int? _budget;
  int? get budget => _$this._budget;
  set budget(int? budget) => _$this._budget = budget;

  DateTime? _startDate;
  DateTime? get startDate => _$this._startDate;
  set startDate(DateTime? startDate) => _$this._startDate = startDate;

  TbProjectBuilder() {
    TbProject._defaults(this);
  }

  TbProjectBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _description = $v.description;
      _address = $v.address;
      _latitude = $v.latitude;
      _longitude = $v.longitude;
      _area = $v.area;
      _style = $v.style;
      _bedrooms = $v.bedrooms;
      _livingrooms = $v.livingrooms;
      _bathrooms = $v.bathrooms;
      _kitchens = $v.kitchens;
      _budget = $v.budget;
      _startDate = $v.startDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbProject other) {
    _$v = other as _$TbProject;
  }

  @override
  void update(void Function(TbProjectBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbProject build() => _build();

  _$TbProject _build() {
    final _$result = _$v ??
        _$TbProject._(
          id: BuiltValueNullFieldError.checkNotNull(id, r'TbProject', 'id'),
          name:
              BuiltValueNullFieldError.checkNotNull(name, r'TbProject', 'name'),
          description: description,
          address: address,
          latitude: latitude,
          longitude: longitude,
          area: area,
          style: style,
          bedrooms: bedrooms,
          livingrooms: livingrooms,
          bathrooms: bathrooms,
          kitchens: kitchens,
          budget: budget,
          startDate: startDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
