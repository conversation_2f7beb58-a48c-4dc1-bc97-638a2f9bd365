// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_response_string.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CreateResponseString extends CreateResponseString {
  @override
  final bool success;
  @override
  final String? id;

  factory _$CreateResponseString(
          [void Function(CreateResponseStringBuilder)? updates]) =>
      (CreateResponseStringBuilder()..update(updates))._build();

  _$CreateResponseString._({required this.success, this.id}) : super._();
  @override
  CreateResponseString rebuild(
          void Function(CreateResponseStringBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateResponseStringBuilder toBuilder() =>
      CreateResponseStringBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateResponseString &&
        success == other.success &&
        id == other.id;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateResponseString')
          ..add('success', success)
          ..add('id', id))
        .toString();
  }
}

class CreateResponseStringBuilder
    implements Builder<CreateResponseString, CreateResponseStringBuilder> {
  _$CreateResponseString? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  CreateResponseStringBuilder() {
    CreateResponseString._defaults(this);
  }

  CreateResponseStringBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _id = $v.id;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateResponseString other) {
    _$v = other as _$CreateResponseString;
  }

  @override
  void update(void Function(CreateResponseStringBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateResponseString build() => _build();

  _$CreateResponseString _build() {
    final _$result = _$v ??
        _$CreateResponseString._(
          success: BuiltValueNullFieldError.checkNotNull(
              success, r'CreateResponseString', 'success'),
          id: id,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
