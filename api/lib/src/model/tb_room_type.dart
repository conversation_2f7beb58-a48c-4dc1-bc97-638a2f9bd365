//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_room_type.g.dart';

/// TbRoomType
///
/// Properties:
/// * [id] 
/// * [name] 
/// * [index] 
/// * [defaultVisiable] 
/// * [shortName] 
/// * [defaultCount] 
@BuiltValue()
abstract class TbRoomType implements Built<TbRoomType, TbRoomTypeBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  @BuiltValueField(wireName: r'name')
  String get name;

  @BuiltValueField(wireName: r'index')
  int? get index;

  @BuiltValueField(wireName: r'defaultVisiable')
  bool? get defaultVisiable;

  @BuiltValueField(wireName: r'shortName')
  String get shortName;

  @BuiltValueField(wireName: r'defaultCount')
  int? get defaultCount;

  TbRoomType._();

  factory TbRoomType([void updates(TbRoomTypeBuilder b)]) = _$TbRoomType;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbRoomTypeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbRoomType> get serializer => _$TbRoomTypeSerializer();
}

class _$TbRoomTypeSerializer implements PrimitiveSerializer<TbRoomType> {
  @override
  final Iterable<Type> types = const [TbRoomType, _$TbRoomType];

  @override
  final String wireName = r'TbRoomType';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbRoomType object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
    yield r'name';
    yield serializers.serialize(
      object.name,
      specifiedType: const FullType(String),
    );
    if (object.index != null) {
      yield r'index';
      yield serializers.serialize(
        object.index,
        specifiedType: const FullType(int),
      );
    }
    if (object.defaultVisiable != null) {
      yield r'defaultVisiable';
      yield serializers.serialize(
        object.defaultVisiable,
        specifiedType: const FullType(bool),
      );
    }
    yield r'shortName';
    yield serializers.serialize(
      object.shortName,
      specifiedType: const FullType(String),
    );
    if (object.defaultCount != null) {
      yield r'defaultCount';
      yield serializers.serialize(
        object.defaultCount,
        specifiedType: const FullType(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbRoomType object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbRoomTypeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'index':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.index = valueDes;
          break;
        case r'defaultVisiable':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.defaultVisiable = valueDes;
          break;
        case r'shortName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.shortName = valueDes;
          break;
        case r'defaultCount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.defaultCount = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbRoomType deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbRoomTypeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

