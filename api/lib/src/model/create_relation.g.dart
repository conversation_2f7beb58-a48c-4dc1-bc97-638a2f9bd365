// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_relation.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CreateRelation extends CreateRelation {
  @override
  final String projectId;
  @override
  final BuiltList<String> roleIds;
  @override
  final String displayName;
  @override
  final String phone;

  factory _$CreateRelation([void Function(CreateRelationBuilder)? updates]) =>
      (CreateRelationBuilder()..update(updates))._build();

  _$CreateRelation._(
      {required this.projectId,
      required this.roleIds,
      required this.displayName,
      required this.phone})
      : super._();
  @override
  CreateRelation rebuild(void Function(CreateRelationBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateRelationBuilder toBuilder() => CreateRelationBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateRelation &&
        projectId == other.projectId &&
        roleIds == other.roleIds &&
        displayName == other.displayName &&
        phone == other.phone;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, projectId.hashCode);
    _$hash = $jc(_$hash, roleIds.hashCode);
    _$hash = $jc(_$hash, displayName.hashCode);
    _$hash = $jc(_$hash, phone.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateRelation')
          ..add('projectId', projectId)
          ..add('roleIds', roleIds)
          ..add('displayName', displayName)
          ..add('phone', phone))
        .toString();
  }
}

class CreateRelationBuilder
    implements Builder<CreateRelation, CreateRelationBuilder> {
  _$CreateRelation? _$v;

  String? _projectId;
  String? get projectId => _$this._projectId;
  set projectId(String? projectId) => _$this._projectId = projectId;

  ListBuilder<String>? _roleIds;
  ListBuilder<String> get roleIds => _$this._roleIds ??= ListBuilder<String>();
  set roleIds(ListBuilder<String>? roleIds) => _$this._roleIds = roleIds;

  String? _displayName;
  String? get displayName => _$this._displayName;
  set displayName(String? displayName) => _$this._displayName = displayName;

  String? _phone;
  String? get phone => _$this._phone;
  set phone(String? phone) => _$this._phone = phone;

  CreateRelationBuilder() {
    CreateRelation._defaults(this);
  }

  CreateRelationBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _projectId = $v.projectId;
      _roleIds = $v.roleIds.toBuilder();
      _displayName = $v.displayName;
      _phone = $v.phone;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateRelation other) {
    _$v = other as _$CreateRelation;
  }

  @override
  void update(void Function(CreateRelationBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateRelation build() => _build();

  _$CreateRelation _build() {
    _$CreateRelation _$result;
    try {
      _$result = _$v ??
          _$CreateRelation._(
            projectId: BuiltValueNullFieldError.checkNotNull(
                projectId, r'CreateRelation', 'projectId'),
            roleIds: roleIds.build(),
            displayName: BuiltValueNullFieldError.checkNotNull(
                displayName, r'CreateRelation', 'displayName'),
            phone: BuiltValueNullFieldError.checkNotNull(
                phone, r'CreateRelation', 'phone'),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'roleIds';
        roleIds.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'CreateRelation', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
