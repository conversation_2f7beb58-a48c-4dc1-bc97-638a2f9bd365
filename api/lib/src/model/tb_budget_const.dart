//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_budget_const.g.dart';

/// TbBudgetConst
///
/// Properties:
/// * [budgetTypeId] 
/// * [areaLaborFactor] 
/// * [areaMaterialFactor] 
/// * [fixedDeviceFactor] 
@BuiltValue()
abstract class TbBudgetConst implements Built<TbBudgetConst, TbBudgetConstBuilder> {
  @BuiltValueField(wireName: r'budgetTypeId')
  String get budgetTypeId;

  @BuiltValueField(wireName: r'areaLaborFactor')
  int? get areaLaborFactor;

  @BuiltValueField(wireName: r'areaMaterialFactor')
  int? get areaMaterialFactor;

  @BuiltValueField(wireName: r'fixedDeviceFactor')
  int? get fixedDeviceFactor;

  TbBudgetConst._();

  factory TbBudgetConst([void updates(TbBudgetConstBuilder b)]) = _$TbBudgetConst;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbBudgetConstBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbBudgetConst> get serializer => _$TbBudgetConstSerializer();
}

class _$TbBudgetConstSerializer implements PrimitiveSerializer<TbBudgetConst> {
  @override
  final Iterable<Type> types = const [TbBudgetConst, _$TbBudgetConst];

  @override
  final String wireName = r'TbBudgetConst';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbBudgetConst object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'budgetTypeId';
    yield serializers.serialize(
      object.budgetTypeId,
      specifiedType: const FullType(String),
    );
    if (object.areaLaborFactor != null) {
      yield r'areaLaborFactor';
      yield serializers.serialize(
        object.areaLaborFactor,
        specifiedType: const FullType(int),
      );
    }
    if (object.areaMaterialFactor != null) {
      yield r'areaMaterialFactor';
      yield serializers.serialize(
        object.areaMaterialFactor,
        specifiedType: const FullType(int),
      );
    }
    if (object.fixedDeviceFactor != null) {
      yield r'fixedDeviceFactor';
      yield serializers.serialize(
        object.fixedDeviceFactor,
        specifiedType: const FullType(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbBudgetConst object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbBudgetConstBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'budgetTypeId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.budgetTypeId = valueDes;
          break;
        case r'areaLaborFactor':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.areaLaborFactor = valueDes;
          break;
        case r'areaMaterialFactor':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.areaMaterialFactor = valueDes;
          break;
        case r'fixedDeviceFactor':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.fixedDeviceFactor = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbBudgetConst deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbBudgetConstBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

