// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_budget_factor.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbBudgetFactor extends TbBudgetFactor {
  @override
  final String budgetTypeId;
  @override
  final String attribute;
  @override
  final int? value;
  @override
  final String key;

  factory _$TbBudgetFactor([void Function(TbBudgetFactorBuilder)? updates]) =>
      (TbBudgetFactorBuilder()..update(updates))._build();

  _$TbBudgetFactor._(
      {required this.budgetTypeId,
      required this.attribute,
      this.value,
      required this.key})
      : super._();
  @override
  TbBudgetFactor rebuild(void Function(TbBudgetFactorBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbBudgetFactorBuilder toBuilder() => TbBudgetFactorBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbBudgetFactor &&
        budgetTypeId == other.budgetTypeId &&
        attribute == other.attribute &&
        value == other.value &&
        key == other.key;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, budgetTypeId.hashCode);
    _$hash = $jc(_$hash, attribute.hashCode);
    _$hash = $jc(_$hash, value.hashCode);
    _$hash = $jc(_$hash, key.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbBudgetFactor')
          ..add('budgetTypeId', budgetTypeId)
          ..add('attribute', attribute)
          ..add('value', value)
          ..add('key', key))
        .toString();
  }
}

class TbBudgetFactorBuilder
    implements Builder<TbBudgetFactor, TbBudgetFactorBuilder> {
  _$TbBudgetFactor? _$v;

  String? _budgetTypeId;
  String? get budgetTypeId => _$this._budgetTypeId;
  set budgetTypeId(String? budgetTypeId) => _$this._budgetTypeId = budgetTypeId;

  String? _attribute;
  String? get attribute => _$this._attribute;
  set attribute(String? attribute) => _$this._attribute = attribute;

  int? _value;
  int? get value => _$this._value;
  set value(int? value) => _$this._value = value;

  String? _key;
  String? get key => _$this._key;
  set key(String? key) => _$this._key = key;

  TbBudgetFactorBuilder() {
    TbBudgetFactor._defaults(this);
  }

  TbBudgetFactorBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _budgetTypeId = $v.budgetTypeId;
      _attribute = $v.attribute;
      _value = $v.value;
      _key = $v.key;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbBudgetFactor other) {
    _$v = other as _$TbBudgetFactor;
  }

  @override
  void update(void Function(TbBudgetFactorBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbBudgetFactor build() => _build();

  _$TbBudgetFactor _build() {
    final _$result = _$v ??
        _$TbBudgetFactor._(
          budgetTypeId: BuiltValueNullFieldError.checkNotNull(
              budgetTypeId, r'TbBudgetFactor', 'budgetTypeId'),
          attribute: BuiltValueNullFieldError.checkNotNull(
              attribute, r'TbBudgetFactor', 'attribute'),
          value: value,
          key: BuiltValueNullFieldError.checkNotNull(
              key, r'TbBudgetFactor', 'key'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
