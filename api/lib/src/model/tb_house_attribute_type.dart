//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_house_attribute_type.g.dart';

/// TbHouseAttributeType
///
/// Properties:
/// * [id] 
/// * [name] 
/// * [index] 
/// * [defaultVisiable] 
@BuiltValue()
abstract class TbHouseAttributeType implements Built<TbHouseAttributeType, TbHouseAttributeTypeBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  @BuiltValueField(wireName: r'name')
  String get name;

  @BuiltValueField(wireName: r'index')
  int? get index;

  @BuiltValueField(wireName: r'defaultVisiable')
  bool? get defaultVisiable;

  TbHouseAttributeType._();

  factory TbHouseAttributeType([void updates(TbHouseAttributeTypeBuilder b)]) = _$TbHouseAttributeType;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbHouseAttributeTypeBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbHouseAttributeType> get serializer => _$TbHouseAttributeTypeSerializer();
}

class _$TbHouseAttributeTypeSerializer implements PrimitiveSerializer<TbHouseAttributeType> {
  @override
  final Iterable<Type> types = const [TbHouseAttributeType, _$TbHouseAttributeType];

  @override
  final String wireName = r'TbHouseAttributeType';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbHouseAttributeType object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
    yield r'name';
    yield serializers.serialize(
      object.name,
      specifiedType: const FullType(String),
    );
    if (object.index != null) {
      yield r'index';
      yield serializers.serialize(
        object.index,
        specifiedType: const FullType(int),
      );
    }
    if (object.defaultVisiable != null) {
      yield r'defaultVisiable';
      yield serializers.serialize(
        object.defaultVisiable,
        specifiedType: const FullType(bool),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbHouseAttributeType object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbHouseAttributeTypeBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'index':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.index = valueDes;
          break;
        case r'defaultVisiable':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.defaultVisiable = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbHouseAttributeType deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbHouseAttributeTypeBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

