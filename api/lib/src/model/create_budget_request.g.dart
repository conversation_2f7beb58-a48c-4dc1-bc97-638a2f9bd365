// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_budget_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CreateBudgetRequest extends CreateBudgetRequest {
  @override
  final String? name;
  @override
  final String? projectId;

  factory _$CreateBudgetRequest(
          [void Function(CreateBudgetRequestBuilder)? updates]) =>
      (CreateBudgetRequestBuilder()..update(updates))._build();

  _$CreateBudgetRequest._({this.name, this.projectId}) : super._();
  @override
  CreateBudgetRequest rebuild(
          void Function(CreateBudgetRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateBudgetRequestBuilder toBuilder() =>
      CreateBudgetRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateBudgetRequest &&
        name == other.name &&
        projectId == other.projectId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, projectId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateBudgetRequest')
          ..add('name', name)
          ..add('projectId', projectId))
        .toString();
  }
}

class CreateBudgetRequestBuilder
    implements Builder<CreateBudgetRequest, CreateBudgetRequestBuilder> {
  _$CreateBudgetRequest? _$v;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _projectId;
  String? get projectId => _$this._projectId;
  set projectId(String? projectId) => _$this._projectId = projectId;

  CreateBudgetRequestBuilder() {
    CreateBudgetRequest._defaults(this);
  }

  CreateBudgetRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _name = $v.name;
      _projectId = $v.projectId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateBudgetRequest other) {
    _$v = other as _$CreateBudgetRequest;
  }

  @override
  void update(void Function(CreateBudgetRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateBudgetRequest build() => _build();

  _$CreateBudgetRequest _build() {
    final _$result = _$v ??
        _$CreateBudgetRequest._(
          name: name,
          projectId: projectId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
