// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_house_attribute.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbHouseAttribute extends TbHouseAttribute {
  @override
  final String? id;
  @override
  final String type;
  @override
  final String name;
  @override
  final int? index;
  @override
  final int? defaultFactor;
  @override
  final bool? isDefault;
  @override
  final String? imageUrl;

  factory _$TbHouseAttribute(
          [void Function(TbHouseAttributeBuilder)? updates]) =>
      (TbHouseAttributeBuilder()..update(updates))._build();

  _$TbHouseAttribute._(
      {this.id,
      required this.type,
      required this.name,
      this.index,
      this.defaultFactor,
      this.isDefault,
      this.imageUrl})
      : super._();
  @override
  TbHouseAttribute rebuild(void Function(TbHouseAttributeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbHouseAttributeBuilder toBuilder() =>
      TbHouseAttributeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbHouseAttribute &&
        id == other.id &&
        type == other.type &&
        name == other.name &&
        index == other.index &&
        defaultFactor == other.defaultFactor &&
        isDefault == other.isDefault &&
        imageUrl == other.imageUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, index.hashCode);
    _$hash = $jc(_$hash, defaultFactor.hashCode);
    _$hash = $jc(_$hash, isDefault.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbHouseAttribute')
          ..add('id', id)
          ..add('type', type)
          ..add('name', name)
          ..add('index', index)
          ..add('defaultFactor', defaultFactor)
          ..add('isDefault', isDefault)
          ..add('imageUrl', imageUrl))
        .toString();
  }
}

class TbHouseAttributeBuilder
    implements Builder<TbHouseAttribute, TbHouseAttributeBuilder> {
  _$TbHouseAttribute? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _type;
  String? get type => _$this._type;
  set type(String? type) => _$this._type = type;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  int? _index;
  int? get index => _$this._index;
  set index(int? index) => _$this._index = index;

  int? _defaultFactor;
  int? get defaultFactor => _$this._defaultFactor;
  set defaultFactor(int? defaultFactor) =>
      _$this._defaultFactor = defaultFactor;

  bool? _isDefault;
  bool? get isDefault => _$this._isDefault;
  set isDefault(bool? isDefault) => _$this._isDefault = isDefault;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  TbHouseAttributeBuilder() {
    TbHouseAttribute._defaults(this);
  }

  TbHouseAttributeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _type = $v.type;
      _name = $v.name;
      _index = $v.index;
      _defaultFactor = $v.defaultFactor;
      _isDefault = $v.isDefault;
      _imageUrl = $v.imageUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbHouseAttribute other) {
    _$v = other as _$TbHouseAttribute;
  }

  @override
  void update(void Function(TbHouseAttributeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbHouseAttribute build() => _build();

  _$TbHouseAttribute _build() {
    final _$result = _$v ??
        _$TbHouseAttribute._(
          id: id,
          type: BuiltValueNullFieldError.checkNotNull(
              type, r'TbHouseAttribute', 'type'),
          name: BuiltValueNullFieldError.checkNotNull(
              name, r'TbHouseAttribute', 'name'),
          index: index,
          defaultFactor: defaultFactor,
          isDefault: isDefault,
          imageUrl: imageUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
