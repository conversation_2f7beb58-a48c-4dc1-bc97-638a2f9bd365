//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_project.g.dart';

/// TbProject
///
/// Properties:
/// * [id] 
/// * [name] 
/// * [description] 
/// * [address] 
/// * [latitude] 
/// * [longitude] 
/// * [area] 
/// * [style] 
/// * [bedrooms] 
/// * [livingrooms] 
/// * [bathrooms] 
/// * [kitchens] 
/// * [budget] 
/// * [startDate] 
@BuiltValue()
abstract class TbProject implements Built<TbProject, TbProjectBuilder> {
  @BuiltValueField(wireName: r'id')
  String get id;

  @BuiltValueField(wireName: r'name')
  String get name;

  @BuiltValueField(wireName: r'description')
  String? get description;

  @BuiltValueField(wireName: r'address')
  String? get address;

  @BuiltValueField(wireName: r'latitude')
  double? get latitude;

  @BuiltValueField(wireName: r'longitude')
  double? get longitude;

  @BuiltValueField(wireName: r'area')
  double? get area;

  @BuiltValueField(wireName: r'style')
  String? get style;

  @BuiltValueField(wireName: r'bedrooms')
  int? get bedrooms;

  @BuiltValueField(wireName: r'livingrooms')
  int? get livingrooms;

  @BuiltValueField(wireName: r'bathrooms')
  int? get bathrooms;

  @BuiltValueField(wireName: r'kitchens')
  int? get kitchens;

  @BuiltValueField(wireName: r'budget')
  int? get budget;

  @BuiltValueField(wireName: r'startDate')
  DateTime? get startDate;

  TbProject._();

  factory TbProject([void updates(TbProjectBuilder b)]) = _$TbProject;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbProjectBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbProject> get serializer => _$TbProjectSerializer();
}

class _$TbProjectSerializer implements PrimitiveSerializer<TbProject> {
  @override
  final Iterable<Type> types = const [TbProject, _$TbProject];

  @override
  final String wireName = r'TbProject';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbProject object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(String),
    );
    yield r'name';
    yield serializers.serialize(
      object.name,
      specifiedType: const FullType(String),
    );
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType(String),
      );
    }
    if (object.address != null) {
      yield r'address';
      yield serializers.serialize(
        object.address,
        specifiedType: const FullType(String),
      );
    }
    if (object.latitude != null) {
      yield r'latitude';
      yield serializers.serialize(
        object.latitude,
        specifiedType: const FullType(double),
      );
    }
    if (object.longitude != null) {
      yield r'longitude';
      yield serializers.serialize(
        object.longitude,
        specifiedType: const FullType(double),
      );
    }
    if (object.area != null) {
      yield r'area';
      yield serializers.serialize(
        object.area,
        specifiedType: const FullType(double),
      );
    }
    if (object.style != null) {
      yield r'style';
      yield serializers.serialize(
        object.style,
        specifiedType: const FullType(String),
      );
    }
    if (object.bedrooms != null) {
      yield r'bedrooms';
      yield serializers.serialize(
        object.bedrooms,
        specifiedType: const FullType(int),
      );
    }
    if (object.livingrooms != null) {
      yield r'livingrooms';
      yield serializers.serialize(
        object.livingrooms,
        specifiedType: const FullType(int),
      );
    }
    if (object.bathrooms != null) {
      yield r'bathrooms';
      yield serializers.serialize(
        object.bathrooms,
        specifiedType: const FullType(int),
      );
    }
    if (object.kitchens != null) {
      yield r'kitchens';
      yield serializers.serialize(
        object.kitchens,
        specifiedType: const FullType(int),
      );
    }
    if (object.budget != null) {
      yield r'budget';
      yield serializers.serialize(
        object.budget,
        specifiedType: const FullType(int),
      );
    }
    if (object.startDate != null) {
      yield r'startDate';
      yield serializers.serialize(
        object.startDate,
        specifiedType: const FullType(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbProject object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbProjectBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.description = valueDes;
          break;
        case r'address':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.address = valueDes;
          break;
        case r'latitude':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.latitude = valueDes;
          break;
        case r'longitude':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.longitude = valueDes;
          break;
        case r'area':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.area = valueDes;
          break;
        case r'style':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.style = valueDes;
          break;
        case r'bedrooms':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.bedrooms = valueDes;
          break;
        case r'livingrooms':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.livingrooms = valueDes;
          break;
        case r'bathrooms':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.bathrooms = valueDes;
          break;
        case r'kitchens':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.kitchens = valueDes;
          break;
        case r'budget':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.budget = valueDes;
          break;
        case r'startDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.startDate = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbProject deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbProjectBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

