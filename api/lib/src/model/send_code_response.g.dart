// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SendCodeResponse extends SendCodeResponse {
  @override
  final bool success;
  @override
  final int? expire;
  @override
  final String? message;

  factory _$SendCodeResponse(
          [void Function(SendCodeResponseBuilder)? updates]) =>
      (SendCodeResponseBuilder()..update(updates))._build();

  _$SendCodeResponse._({required this.success, this.expire, this.message})
      : super._();
  @override
  SendCodeResponse rebuild(void Function(SendCodeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SendCodeResponseBuilder toBuilder() =>
      SendCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SendCodeResponse &&
        success == other.success &&
        expire == other.expire &&
        message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, expire.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SendCodeResponse')
          ..add('success', success)
          ..add('expire', expire)
          ..add('message', message))
        .toString();
  }
}

class SendCodeResponseBuilder
    implements Builder<SendCodeResponse, SendCodeResponseBuilder> {
  _$SendCodeResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _expire;
  int? get expire => _$this._expire;
  set expire(int? expire) => _$this._expire = expire;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  SendCodeResponseBuilder() {
    SendCodeResponse._defaults(this);
  }

  SendCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _expire = $v.expire;
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SendCodeResponse other) {
    _$v = other as _$SendCodeResponse;
  }

  @override
  void update(void Function(SendCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SendCodeResponse build() => _build();

  _$SendCodeResponse _build() {
    final _$result = _$v ??
        _$SendCodeResponse._(
          success: BuiltValueNullFieldError.checkNotNull(
              success, r'SendCodeResponse', 'success'),
          expire: expire,
          message: message,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
