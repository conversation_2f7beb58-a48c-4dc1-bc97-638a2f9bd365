// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upsert_project_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpsertProjectRequest extends UpsertProjectRequest {
  @override
  final TbProject project;
  @override
  final String creatorName;
  @override
  final BuiltList<String>? creatorRoles;

  factory _$UpsertProjectRequest(
          [void Function(UpsertProjectRequestBuilder)? updates]) =>
      (UpsertProjectRequestBuilder()..update(updates))._build();

  _$UpsertProjectRequest._(
      {required this.project, required this.creatorName, this.creatorRoles})
      : super._();
  @override
  UpsertProjectRequest rebuild(
          void Function(UpsertProjectRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpsertProjectRequestBuilder toBuilder() =>
      UpsertProjectRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpsertProjectRequest &&
        project == other.project &&
        creatorName == other.creatorName &&
        creatorRoles == other.creatorRoles;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, project.hashCode);
    _$hash = $jc(_$hash, creatorName.hashCode);
    _$hash = $jc(_$hash, creatorRoles.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpsertProjectRequest')
          ..add('project', project)
          ..add('creatorName', creatorName)
          ..add('creatorRoles', creatorRoles))
        .toString();
  }
}

class UpsertProjectRequestBuilder
    implements Builder<UpsertProjectRequest, UpsertProjectRequestBuilder> {
  _$UpsertProjectRequest? _$v;

  TbProjectBuilder? _project;
  TbProjectBuilder get project => _$this._project ??= TbProjectBuilder();
  set project(TbProjectBuilder? project) => _$this._project = project;

  String? _creatorName;
  String? get creatorName => _$this._creatorName;
  set creatorName(String? creatorName) => _$this._creatorName = creatorName;

  ListBuilder<String>? _creatorRoles;
  ListBuilder<String> get creatorRoles =>
      _$this._creatorRoles ??= ListBuilder<String>();
  set creatorRoles(ListBuilder<String>? creatorRoles) =>
      _$this._creatorRoles = creatorRoles;

  UpsertProjectRequestBuilder() {
    UpsertProjectRequest._defaults(this);
  }

  UpsertProjectRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _project = $v.project.toBuilder();
      _creatorName = $v.creatorName;
      _creatorRoles = $v.creatorRoles?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpsertProjectRequest other) {
    _$v = other as _$UpsertProjectRequest;
  }

  @override
  void update(void Function(UpsertProjectRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpsertProjectRequest build() => _build();

  _$UpsertProjectRequest _build() {
    _$UpsertProjectRequest _$result;
    try {
      _$result = _$v ??
          _$UpsertProjectRequest._(
            project: project.build(),
            creatorName: BuiltValueNullFieldError.checkNotNull(
                creatorName, r'UpsertProjectRequest', 'creatorName'),
            creatorRoles: _creatorRoles?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'project';
        project.build();

        _$failedField = 'creatorRoles';
        _creatorRoles?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'UpsertProjectRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
