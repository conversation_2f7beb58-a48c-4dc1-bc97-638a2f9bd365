//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_budget.g.dart';

/// TbBudget
///
/// Properties:
/// * [id] 
/// * [projectId] 
/// * [name] 
/// * [description] 
/// * [createdByUser] 
/// * [createAt] 
/// * [updateAt] 
@BuiltValue()
abstract class TbBudget implements Built<TbBudget, TbBudgetBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  @BuiltValueField(wireName: r'projectId')
  String? get projectId;

  @BuiltValueField(wireName: r'name')
  String get name;

  @BuiltValueField(wireName: r'description')
  String? get description;

  @BuiltValueField(wireName: r'createdByUser')
  String get createdByUser;

  @BuiltValueField(wireName: r'createAt')
  DateTime? get createAt;

  @BuiltValueField(wireName: r'updateAt')
  DateTime? get updateAt;

  TbBudget._();

  factory TbBudget([void updates(TbBudgetBuilder b)]) = _$TbBudget;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbBudgetBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbBudget> get serializer => _$TbBudgetSerializer();
}

class _$TbBudgetSerializer implements PrimitiveSerializer<TbBudget> {
  @override
  final Iterable<Type> types = const [TbBudget, _$TbBudget];

  @override
  final String wireName = r'TbBudget';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbBudget object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
    if (object.projectId != null) {
      yield r'projectId';
      yield serializers.serialize(
        object.projectId,
        specifiedType: const FullType(String),
      );
    }
    yield r'name';
    yield serializers.serialize(
      object.name,
      specifiedType: const FullType(String),
    );
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType(String),
      );
    }
    yield r'createdByUser';
    yield serializers.serialize(
      object.createdByUser,
      specifiedType: const FullType(String),
    );
    if (object.createAt != null) {
      yield r'createAt';
      yield serializers.serialize(
        object.createAt,
        specifiedType: const FullType(DateTime),
      );
    }
    if (object.updateAt != null) {
      yield r'updateAt';
      yield serializers.serialize(
        object.updateAt,
        specifiedType: const FullType(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbBudget object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbBudgetBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'projectId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.projectId = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.description = valueDes;
          break;
        case r'createdByUser':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.createdByUser = valueDes;
          break;
        case r'createAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.createAt = valueDes;
          break;
        case r'updateAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.updateAt = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbBudget deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbBudgetBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

