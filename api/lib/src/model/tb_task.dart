//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/json_object.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_task.g.dart';

/// TbTask
///
/// Properties:
/// * [title] 
/// * [assignToRole] 
/// * [createTime] 
/// * [targetTime] 
/// * [dueTime] 
/// * [groupId] 
/// * [createUser] 
/// * [planId] 
/// * [form] 
@BuiltValue()
abstract class TbTask implements Built<TbTask, TbTaskBuilder> {
  @BuiltValueField(wireName: r'title')
  String get title;

  @BuiltValueField(wireName: r'assignToRole')
  String get assignToRole;

  @BuiltValueField(wireName: r'createTime')
  DateTime? get createTime;

  @BuiltValueField(wireName: r'targetTime')
  DateTime? get targetTime;

  @BuiltValueField(wireName: r'dueTime')
  DateTime? get dueTime;

  @BuiltValueField(wireName: r'groupId')
  String get groupId;

  @BuiltValueField(wireName: r'createUser')
  String get createUser;

  @BuiltValueField(wireName: r'planId')
  String? get planId;

  @BuiltValueField(wireName: r'form')
  JsonObject? get form;

  TbTask._();

  factory TbTask([void updates(TbTaskBuilder b)]) = _$TbTask;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbTaskBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbTask> get serializer => _$TbTaskSerializer();
}

class _$TbTaskSerializer implements PrimitiveSerializer<TbTask> {
  @override
  final Iterable<Type> types = const [TbTask, _$TbTask];

  @override
  final String wireName = r'TbTask';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbTask object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'title';
    yield serializers.serialize(
      object.title,
      specifiedType: const FullType(String),
    );
    yield r'assignToRole';
    yield serializers.serialize(
      object.assignToRole,
      specifiedType: const FullType(String),
    );
    if (object.createTime != null) {
      yield r'createTime';
      yield serializers.serialize(
        object.createTime,
        specifiedType: const FullType(DateTime),
      );
    }
    if (object.targetTime != null) {
      yield r'targetTime';
      yield serializers.serialize(
        object.targetTime,
        specifiedType: const FullType(DateTime),
      );
    }
    if (object.dueTime != null) {
      yield r'dueTime';
      yield serializers.serialize(
        object.dueTime,
        specifiedType: const FullType(DateTime),
      );
    }
    yield r'groupId';
    yield serializers.serialize(
      object.groupId,
      specifiedType: const FullType(String),
    );
    yield r'createUser';
    yield serializers.serialize(
      object.createUser,
      specifiedType: const FullType(String),
    );
    if (object.planId != null) {
      yield r'planId';
      yield serializers.serialize(
        object.planId,
        specifiedType: const FullType(String),
      );
    }
    if (object.form != null) {
      yield r'form';
      yield serializers.serialize(
        object.form,
        specifiedType: const FullType.nullable(JsonObject),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbTask object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbTaskBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.title = valueDes;
          break;
        case r'assignToRole':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.assignToRole = valueDes;
          break;
        case r'createTime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.createTime = valueDes;
          break;
        case r'targetTime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.targetTime = valueDes;
          break;
        case r'dueTime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.dueTime = valueDes;
          break;
        case r'groupId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.groupId = valueDes;
          break;
        case r'createUser':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.createUser = valueDes;
          break;
        case r'planId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.planId = valueDes;
          break;
        case r'form':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(JsonObject),
          ) as JsonObject?;
          if (valueDes == null) continue;
          result.form = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbTask deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbTaskBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

