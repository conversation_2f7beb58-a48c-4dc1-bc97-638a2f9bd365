// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_plan_item.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbPlanItem extends TbPlanItem {
  @override
  final String id;
  @override
  final String? parentId;
  @override
  final String title;
  @override
  final String? description;
  @override
  final int offsetDays;
  @override
  final int durationDays;
  @override
  final bool? isCompleted;

  factory _$TbPlanItem([void Function(TbPlanItemBuilder)? updates]) =>
      (TbPlanItemBuilder()..update(updates))._build();

  _$TbPlanItem._(
      {required this.id,
      this.parentId,
      required this.title,
      this.description,
      required this.offsetDays,
      required this.durationDays,
      this.isCompleted})
      : super._();
  @override
  TbPlanItem rebuild(void Function(TbPlanItemBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbPlanItemBuilder toBuilder() => TbPlanItemBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbPlanItem &&
        id == other.id &&
        parentId == other.parentId &&
        title == other.title &&
        description == other.description &&
        offsetDays == other.offsetDays &&
        durationDays == other.durationDays &&
        isCompleted == other.isCompleted;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, offsetDays.hashCode);
    _$hash = $jc(_$hash, durationDays.hashCode);
    _$hash = $jc(_$hash, isCompleted.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbPlanItem')
          ..add('id', id)
          ..add('parentId', parentId)
          ..add('title', title)
          ..add('description', description)
          ..add('offsetDays', offsetDays)
          ..add('durationDays', durationDays)
          ..add('isCompleted', isCompleted))
        .toString();
  }
}

class TbPlanItemBuilder implements Builder<TbPlanItem, TbPlanItemBuilder> {
  _$TbPlanItem? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _offsetDays;
  int? get offsetDays => _$this._offsetDays;
  set offsetDays(int? offsetDays) => _$this._offsetDays = offsetDays;

  int? _durationDays;
  int? get durationDays => _$this._durationDays;
  set durationDays(int? durationDays) => _$this._durationDays = durationDays;

  bool? _isCompleted;
  bool? get isCompleted => _$this._isCompleted;
  set isCompleted(bool? isCompleted) => _$this._isCompleted = isCompleted;

  TbPlanItemBuilder() {
    TbPlanItem._defaults(this);
  }

  TbPlanItemBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _parentId = $v.parentId;
      _title = $v.title;
      _description = $v.description;
      _offsetDays = $v.offsetDays;
      _durationDays = $v.durationDays;
      _isCompleted = $v.isCompleted;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbPlanItem other) {
    _$v = other as _$TbPlanItem;
  }

  @override
  void update(void Function(TbPlanItemBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbPlanItem build() => _build();

  _$TbPlanItem _build() {
    final _$result = _$v ??
        _$TbPlanItem._(
          id: BuiltValueNullFieldError.checkNotNull(id, r'TbPlanItem', 'id'),
          parentId: parentId,
          title: BuiltValueNullFieldError.checkNotNull(
              title, r'TbPlanItem', 'title'),
          description: description,
          offsetDays: BuiltValueNullFieldError.checkNotNull(
              offsetDays, r'TbPlanItem', 'offsetDays'),
          durationDays: BuiltValueNullFieldError.checkNotNull(
              durationDays, r'TbPlanItem', 'durationDays'),
          isCompleted: isCompleted,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
