// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_role.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbRole extends TbRole {
  @override
  final String id;
  @override
  final String name;
  @override
  final int index;
  @override
  final String? projectId;

  factory _$TbRole([void Function(TbRoleBuilder)? updates]) =>
      (TbRoleBuilder()..update(updates))._build();

  _$TbRole._(
      {required this.id,
      required this.name,
      required this.index,
      this.projectId})
      : super._();
  @override
  TbRole rebuild(void Function(TbRoleBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbRoleBuilder toBuilder() => TbRoleBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbRole &&
        id == other.id &&
        name == other.name &&
        index == other.index &&
        projectId == other.projectId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, index.hashCode);
    _$hash = $jc(_$hash, projectId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbRole')
          ..add('id', id)
          ..add('name', name)
          ..add('index', index)
          ..add('projectId', projectId))
        .toString();
  }
}

class TbRoleBuilder implements Builder<TbRole, TbRoleBuilder> {
  _$TbRole? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  int? _index;
  int? get index => _$this._index;
  set index(int? index) => _$this._index = index;

  String? _projectId;
  String? get projectId => _$this._projectId;
  set projectId(String? projectId) => _$this._projectId = projectId;

  TbRoleBuilder() {
    TbRole._defaults(this);
  }

  TbRoleBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _index = $v.index;
      _projectId = $v.projectId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbRole other) {
    _$v = other as _$TbRole;
  }

  @override
  void update(void Function(TbRoleBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbRole build() => _build();

  _$TbRole _build() {
    final _$result = _$v ??
        _$TbRole._(
          id: BuiltValueNullFieldError.checkNotNull(id, r'TbRole', 'id'),
          name: BuiltValueNullFieldError.checkNotNull(name, r'TbRole', 'name'),
          index:
              BuiltValueNullFieldError.checkNotNull(index, r'TbRole', 'index'),
          projectId: projectId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
