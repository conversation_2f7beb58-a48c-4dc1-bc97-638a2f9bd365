// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_task_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$CreateTaskRequest extends CreateTaskRequest {
  @override
  final String title;
  @override
  final String groupId;
  @override
  final String? planId;
  @override
  final BuiltList<String> assignToRoles;
  @override
  final bool confirmRequired;
  @override
  final BuiltList<String> messages;
  @override
  final BuiltList<String> attachments;

  factory _$CreateTaskRequest(
          [void Function(CreateTaskRequestBuilder)? updates]) =>
      (CreateTaskRequestBuilder()..update(updates))._build();

  _$CreateTaskRequest._(
      {required this.title,
      required this.groupId,
      this.planId,
      required this.assignToRoles,
      required this.confirmRequired,
      required this.messages,
      required this.attachments})
      : super._();
  @override
  CreateTaskRequest rebuild(void Function(CreateTaskRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateTaskRequestBuilder toBuilder() =>
      CreateTaskRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateTaskRequest &&
        title == other.title &&
        groupId == other.groupId &&
        planId == other.planId &&
        assignToRoles == other.assignToRoles &&
        confirmRequired == other.confirmRequired &&
        messages == other.messages &&
        attachments == other.attachments;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, groupId.hashCode);
    _$hash = $jc(_$hash, planId.hashCode);
    _$hash = $jc(_$hash, assignToRoles.hashCode);
    _$hash = $jc(_$hash, confirmRequired.hashCode);
    _$hash = $jc(_$hash, messages.hashCode);
    _$hash = $jc(_$hash, attachments.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateTaskRequest')
          ..add('title', title)
          ..add('groupId', groupId)
          ..add('planId', planId)
          ..add('assignToRoles', assignToRoles)
          ..add('confirmRequired', confirmRequired)
          ..add('messages', messages)
          ..add('attachments', attachments))
        .toString();
  }
}

class CreateTaskRequestBuilder
    implements Builder<CreateTaskRequest, CreateTaskRequestBuilder> {
  _$CreateTaskRequest? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _groupId;
  String? get groupId => _$this._groupId;
  set groupId(String? groupId) => _$this._groupId = groupId;

  String? _planId;
  String? get planId => _$this._planId;
  set planId(String? planId) => _$this._planId = planId;

  ListBuilder<String>? _assignToRoles;
  ListBuilder<String> get assignToRoles =>
      _$this._assignToRoles ??= ListBuilder<String>();
  set assignToRoles(ListBuilder<String>? assignToRoles) =>
      _$this._assignToRoles = assignToRoles;

  bool? _confirmRequired;
  bool? get confirmRequired => _$this._confirmRequired;
  set confirmRequired(bool? confirmRequired) =>
      _$this._confirmRequired = confirmRequired;

  ListBuilder<String>? _messages;
  ListBuilder<String> get messages =>
      _$this._messages ??= ListBuilder<String>();
  set messages(ListBuilder<String>? messages) => _$this._messages = messages;

  ListBuilder<String>? _attachments;
  ListBuilder<String> get attachments =>
      _$this._attachments ??= ListBuilder<String>();
  set attachments(ListBuilder<String>? attachments) =>
      _$this._attachments = attachments;

  CreateTaskRequestBuilder() {
    CreateTaskRequest._defaults(this);
  }

  CreateTaskRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _groupId = $v.groupId;
      _planId = $v.planId;
      _assignToRoles = $v.assignToRoles.toBuilder();
      _confirmRequired = $v.confirmRequired;
      _messages = $v.messages.toBuilder();
      _attachments = $v.attachments.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateTaskRequest other) {
    _$v = other as _$CreateTaskRequest;
  }

  @override
  void update(void Function(CreateTaskRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateTaskRequest build() => _build();

  _$CreateTaskRequest _build() {
    _$CreateTaskRequest _$result;
    try {
      _$result = _$v ??
          _$CreateTaskRequest._(
            title: BuiltValueNullFieldError.checkNotNull(
                title, r'CreateTaskRequest', 'title'),
            groupId: BuiltValueNullFieldError.checkNotNull(
                groupId, r'CreateTaskRequest', 'groupId'),
            planId: planId,
            assignToRoles: assignToRoles.build(),
            confirmRequired: BuiltValueNullFieldError.checkNotNull(
                confirmRequired, r'CreateTaskRequest', 'confirmRequired'),
            messages: messages.build(),
            attachments: attachments.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'assignToRoles';
        assignToRoles.build();

        _$failedField = 'messages';
        messages.build();
        _$failedField = 'attachments';
        attachments.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'CreateTaskRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
