//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_region.g.dart';

/// TbRegion
///
/// Properties:
/// * [id] 
/// * [name] 
/// * [type] 
/// * [parent] 
/// * [centerLat] 
/// * [centerLon] 
/// * [laborIndex] 
/// * [materialIndex] 
@BuiltValue()
abstract class TbRegion implements Built<TbRegion, TbRegionBuilder> {
  @BuiltValueField(wireName: r'id')
  String get id;

  @BuiltValueField(wireName: r'name')
  String get name;

  @BuiltValueField(wireName: r'type')
  TbRegionTypeEnum get type;
  // enum typeEnum {  country,  province,  city,  district,  };

  @BuiltValueField(wireName: r'parent')
  String? get parent;

  @BuiltValueField(wireName: r'centerLat')
  double get centerLat;

  @BuiltValueField(wireName: r'centerLon')
  double get centerLon;

  @BuiltValueField(wireName: r'laborIndex')
  int? get laborIndex;

  @BuiltValueField(wireName: r'materialIndex_')
  int? get materialIndex;

  TbRegion._();

  factory TbRegion([void updates(TbRegionBuilder b)]) = _$TbRegion;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbRegionBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbRegion> get serializer => _$TbRegionSerializer();
}

class _$TbRegionSerializer implements PrimitiveSerializer<TbRegion> {
  @override
  final Iterable<Type> types = const [TbRegion, _$TbRegion];

  @override
  final String wireName = r'TbRegion';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbRegion object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(String),
    );
    yield r'name';
    yield serializers.serialize(
      object.name,
      specifiedType: const FullType(String),
    );
    yield r'type';
    yield serializers.serialize(
      object.type,
      specifiedType: const FullType(TbRegionTypeEnum),
    );
    if (object.parent != null) {
      yield r'parent';
      yield serializers.serialize(
        object.parent,
        specifiedType: const FullType(String),
      );
    }
    yield r'centerLat';
    yield serializers.serialize(
      object.centerLat,
      specifiedType: const FullType(double),
    );
    yield r'centerLon';
    yield serializers.serialize(
      object.centerLon,
      specifiedType: const FullType(double),
    );
    if (object.laborIndex != null) {
      yield r'laborIndex';
      yield serializers.serialize(
        object.laborIndex,
        specifiedType: const FullType(int),
      );
    }
    if (object.materialIndex != null) {
      yield r'materialIndex_';
      yield serializers.serialize(
        object.materialIndex,
        specifiedType: const FullType(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbRegion object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbRegionBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(TbRegionTypeEnum),
          ) as TbRegionTypeEnum;
          result.type = valueDes;
          break;
        case r'parent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.parent = valueDes;
          break;
        case r'centerLat':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.centerLat = valueDes;
          break;
        case r'centerLon':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.centerLon = valueDes;
          break;
        case r'laborIndex':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.laborIndex = valueDes;
          break;
        case r'materialIndex_':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.materialIndex = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbRegion deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbRegionBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class TbRegionTypeEnum extends EnumClass {

  @BuiltValueEnumConst(wireName: r'country')
  static const TbRegionTypeEnum country = _$tbRegionTypeEnum_country;
  @BuiltValueEnumConst(wireName: r'province')
  static const TbRegionTypeEnum province = _$tbRegionTypeEnum_province;
  @BuiltValueEnumConst(wireName: r'city')
  static const TbRegionTypeEnum city = _$tbRegionTypeEnum_city;
  @BuiltValueEnumConst(wireName: r'district')
  static const TbRegionTypeEnum district = _$tbRegionTypeEnum_district;

  static Serializer<TbRegionTypeEnum> get serializer => _$tbRegionTypeEnumSerializer;

  const TbRegionTypeEnum._(String name): super(name);

  static BuiltSet<TbRegionTypeEnum> get values => _$tbRegionTypeEnumValues;
  static TbRegionTypeEnum valueOf(String name) => _$tbRegionTypeEnumValueOf(name);
}

