// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_house_attribute_type.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbHouseAttributeType extends TbHouseAttributeType {
  @override
  final String? id;
  @override
  final String name;
  @override
  final int? index;
  @override
  final bool? defaultVisiable;

  factory _$TbHouseAttributeType(
          [void Function(TbHouseAttributeTypeBuilder)? updates]) =>
      (TbHouseAttributeTypeBuilder()..update(updates))._build();

  _$TbHouseAttributeType._(
      {this.id, required this.name, this.index, this.defaultVisiable})
      : super._();
  @override
  TbHouseAttributeType rebuild(
          void Function(TbHouseAttributeTypeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbHouseAttributeTypeBuilder toBuilder() =>
      TbHouseAttributeTypeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbHouseAttributeType &&
        id == other.id &&
        name == other.name &&
        index == other.index &&
        defaultVisiable == other.defaultVisiable;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, index.hashCode);
    _$hash = $jc(_$hash, defaultVisiable.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbHouseAttributeType')
          ..add('id', id)
          ..add('name', name)
          ..add('index', index)
          ..add('defaultVisiable', defaultVisiable))
        .toString();
  }
}

class TbHouseAttributeTypeBuilder
    implements Builder<TbHouseAttributeType, TbHouseAttributeTypeBuilder> {
  _$TbHouseAttributeType? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  int? _index;
  int? get index => _$this._index;
  set index(int? index) => _$this._index = index;

  bool? _defaultVisiable;
  bool? get defaultVisiable => _$this._defaultVisiable;
  set defaultVisiable(bool? defaultVisiable) =>
      _$this._defaultVisiable = defaultVisiable;

  TbHouseAttributeTypeBuilder() {
    TbHouseAttributeType._defaults(this);
  }

  TbHouseAttributeTypeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _index = $v.index;
      _defaultVisiable = $v.defaultVisiable;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbHouseAttributeType other) {
    _$v = other as _$TbHouseAttributeType;
  }

  @override
  void update(void Function(TbHouseAttributeTypeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbHouseAttributeType build() => _build();

  _$TbHouseAttributeType _build() {
    final _$result = _$v ??
        _$TbHouseAttributeType._(
          id: id,
          name: BuiltValueNullFieldError.checkNotNull(
              name, r'TbHouseAttributeType', 'name'),
          index: index,
          defaultVisiable: defaultVisiable,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
