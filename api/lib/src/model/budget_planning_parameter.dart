//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'budget_planning_parameter.g.dart';

/// BudgetPlanningParameter
///
/// Properties:
/// * [budgetId] 
/// * [area] 
/// * [region] 
/// * [budgetAmount] 
/// * [attributeTypeValues] 
/// * [rooms] 
@BuiltValue()
abstract class BudgetPlanningParameter implements Built<BudgetPlanningParameter, BudgetPlanningParameterBuilder> {
  @BuiltValueField(wireName: r'budgetId')
  String get budgetId;

  @BuiltValueField(wireName: r'area')
  double get area;

  @BuiltValueField(wireName: r'region')
  String get region;

  @BuiltValueField(wireName: r'budgetAmount')
  double? get budgetAmount;

  @BuiltValueField(wireName: r'attributeTypeValues')
  BuiltMap<String, String> get attributeTypeValues;

  @BuiltValueField(wireName: r'rooms')
  BuiltMap<String, int> get rooms;

  BudgetPlanningParameter._();

  factory BudgetPlanningParameter([void updates(BudgetPlanningParameterBuilder b)]) = _$BudgetPlanningParameter;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BudgetPlanningParameterBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BudgetPlanningParameter> get serializer => _$BudgetPlanningParameterSerializer();
}

class _$BudgetPlanningParameterSerializer implements PrimitiveSerializer<BudgetPlanningParameter> {
  @override
  final Iterable<Type> types = const [BudgetPlanningParameter, _$BudgetPlanningParameter];

  @override
  final String wireName = r'BudgetPlanningParameter';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BudgetPlanningParameter object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'budgetId';
    yield serializers.serialize(
      object.budgetId,
      specifiedType: const FullType(String),
    );
    yield r'area';
    yield serializers.serialize(
      object.area,
      specifiedType: const FullType(double),
    );
    yield r'region';
    yield serializers.serialize(
      object.region,
      specifiedType: const FullType(String),
    );
    if (object.budgetAmount != null) {
      yield r'budgetAmount';
      yield serializers.serialize(
        object.budgetAmount,
        specifiedType: const FullType(double),
      );
    }
    yield r'attributeTypeValues';
    yield serializers.serialize(
      object.attributeTypeValues,
      specifiedType: const FullType(BuiltMap, [FullType(String), FullType(String)]),
    );
    yield r'rooms';
    yield serializers.serialize(
      object.rooms,
      specifiedType: const FullType(BuiltMap, [FullType(String), FullType(int)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    BudgetPlanningParameter object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BudgetPlanningParameterBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'budgetId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.budgetId = valueDes;
          break;
        case r'area':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.area = valueDes;
          break;
        case r'region':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.region = valueDes;
          break;
        case r'budgetAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.budgetAmount = valueDes;
          break;
        case r'attributeTypeValues':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltMap, [FullType(String), FullType(String)]),
          ) as BuiltMap<String, String>;
          result.attributeTypeValues.replace(valueDes);
          break;
        case r'rooms':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltMap, [FullType(String), FullType(int)]),
          ) as BuiltMap<String, int>;
          result.rooms.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BudgetPlanningParameter deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BudgetPlanningParameterBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

