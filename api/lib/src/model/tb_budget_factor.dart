//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_budget_factor.g.dart';

/// TbBudgetFactor
///
/// Properties:
/// * [budgetTypeId] 
/// * [attribute] 
/// * [value] 
/// * [key] 
@BuiltValue()
abstract class TbBudgetFactor implements Built<TbBudgetFactor, TbBudgetFactorBuilder> {
  @BuiltValueField(wireName: r'budgetTypeId')
  String get budgetTypeId;

  @BuiltValueField(wireName: r'attribute')
  String get attribute;

  @BuiltValueField(wireName: r'value')
  int? get value;

  @BuiltValueField(wireName: r'key')
  String get key;

  TbBudgetFactor._();

  factory TbBudgetFactor([void updates(TbBudgetFactorBuilder b)]) = _$TbBudgetFactor;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbBudgetFactorBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbBudgetFactor> get serializer => _$TbBudgetFactorSerializer();
}

class _$TbBudgetFactorSerializer implements PrimitiveSerializer<TbBudgetFactor> {
  @override
  final Iterable<Type> types = const [TbBudgetFactor, _$TbBudgetFactor];

  @override
  final String wireName = r'TbBudgetFactor';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbBudgetFactor object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'budgetTypeId';
    yield serializers.serialize(
      object.budgetTypeId,
      specifiedType: const FullType(String),
    );
    yield r'attribute';
    yield serializers.serialize(
      object.attribute,
      specifiedType: const FullType(String),
    );
    if (object.value != null) {
      yield r'value';
      yield serializers.serialize(
        object.value,
        specifiedType: const FullType(int),
      );
    }
    yield r'key';
    yield serializers.serialize(
      object.key,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    TbBudgetFactor object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbBudgetFactorBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'budgetTypeId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.budgetTypeId = valueDes;
          break;
        case r'attribute':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.attribute = valueDes;
          break;
        case r'value':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.value = valueDes;
          break;
        case r'key':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.key = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbBudgetFactor deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbBudgetFactorBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

