//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'tb_budget_room_const.g.dart';

/// TbBudgetRoomConst
///
/// Properties:
/// * [budgetTypeId] 
/// * [roomId] 
/// * [roomLaborFactor] 
/// * [roomMaterialFactor] 
@BuiltValue()
abstract class TbBudgetRoomConst implements Built<TbBudgetRoomConst, TbBudgetRoomConstBuilder> {
  @BuiltValueField(wireName: r'budgetTypeId')
  String get budgetTypeId;

  @BuiltValueField(wireName: r'roomId')
  String get roomId;

  @BuiltValueField(wireName: r'roomLaborFactor')
  int? get roomLaborFactor;

  @BuiltValueField(wireName: r'roomMaterialFactor')
  int? get roomMaterialFactor;

  TbBudgetRoomConst._();

  factory TbBudgetRoomConst([void updates(TbBudgetRoomConstBuilder b)]) = _$TbBudgetRoomConst;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TbBudgetRoomConstBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TbBudgetRoomConst> get serializer => _$TbBudgetRoomConstSerializer();
}

class _$TbBudgetRoomConstSerializer implements PrimitiveSerializer<TbBudgetRoomConst> {
  @override
  final Iterable<Type> types = const [TbBudgetRoomConst, _$TbBudgetRoomConst];

  @override
  final String wireName = r'TbBudgetRoomConst';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TbBudgetRoomConst object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'budgetTypeId';
    yield serializers.serialize(
      object.budgetTypeId,
      specifiedType: const FullType(String),
    );
    yield r'roomId';
    yield serializers.serialize(
      object.roomId,
      specifiedType: const FullType(String),
    );
    if (object.roomLaborFactor != null) {
      yield r'roomLaborFactor';
      yield serializers.serialize(
        object.roomLaborFactor,
        specifiedType: const FullType(int),
      );
    }
    if (object.roomMaterialFactor != null) {
      yield r'roomMaterialFactor';
      yield serializers.serialize(
        object.roomMaterialFactor,
        specifiedType: const FullType(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TbBudgetRoomConst object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TbBudgetRoomConstBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'budgetTypeId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.budgetTypeId = valueDes;
          break;
        case r'roomId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.roomId = valueDes;
          break;
        case r'roomLaborFactor':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.roomLaborFactor = valueDes;
          break;
        case r'roomMaterialFactor':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.roomMaterialFactor = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TbBudgetRoomConst deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TbBudgetRoomConstBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

