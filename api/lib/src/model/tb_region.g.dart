// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_region.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const TbRegionTypeEnum _$tbRegionTypeEnum_country =
    const TbRegionTypeEnum._('country');
const TbRegionTypeEnum _$tbRegionTypeEnum_province =
    const TbRegionTypeEnum._('province');
const TbRegionTypeEnum _$tbRegionTypeEnum_city =
    const TbRegionTypeEnum._('city');
const TbRegionTypeEnum _$tbRegionTypeEnum_district =
    const TbRegionTypeEnum._('district');

TbRegionTypeEnum _$tbRegionTypeEnumValueOf(String name) {
  switch (name) {
    case 'country':
      return _$tbRegionTypeEnum_country;
    case 'province':
      return _$tbRegionTypeEnum_province;
    case 'city':
      return _$tbRegionTypeEnum_city;
    case 'district':
      return _$tbRegionTypeEnum_district;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<TbRegionTypeEnum> _$tbRegionTypeEnumValues =
    BuiltSet<TbRegionTypeEnum>(const <TbRegionTypeEnum>[
  _$tbRegionTypeEnum_country,
  _$tbRegionTypeEnum_province,
  _$tbRegionTypeEnum_city,
  _$tbRegionTypeEnum_district,
]);

Serializer<TbRegionTypeEnum> _$tbRegionTypeEnumSerializer =
    _$TbRegionTypeEnumSerializer();

class _$TbRegionTypeEnumSerializer
    implements PrimitiveSerializer<TbRegionTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'country': 'country',
    'province': 'province',
    'city': 'city',
    'district': 'district',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'country': 'country',
    'province': 'province',
    'city': 'city',
    'district': 'district',
  };

  @override
  final Iterable<Type> types = const <Type>[TbRegionTypeEnum];
  @override
  final String wireName = 'TbRegionTypeEnum';

  @override
  Object serialize(Serializers serializers, TbRegionTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  TbRegionTypeEnum deserialize(Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      TbRegionTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$TbRegion extends TbRegion {
  @override
  final String id;
  @override
  final String name;
  @override
  final TbRegionTypeEnum type;
  @override
  final String? parent;
  @override
  final double centerLat;
  @override
  final double centerLon;
  @override
  final int? laborIndex;
  @override
  final int? materialIndex;

  factory _$TbRegion([void Function(TbRegionBuilder)? updates]) =>
      (TbRegionBuilder()..update(updates))._build();

  _$TbRegion._(
      {required this.id,
      required this.name,
      required this.type,
      this.parent,
      required this.centerLat,
      required this.centerLon,
      this.laborIndex,
      this.materialIndex})
      : super._();
  @override
  TbRegion rebuild(void Function(TbRegionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbRegionBuilder toBuilder() => TbRegionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbRegion &&
        id == other.id &&
        name == other.name &&
        type == other.type &&
        parent == other.parent &&
        centerLat == other.centerLat &&
        centerLon == other.centerLon &&
        laborIndex == other.laborIndex &&
        materialIndex == other.materialIndex;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, parent.hashCode);
    _$hash = $jc(_$hash, centerLat.hashCode);
    _$hash = $jc(_$hash, centerLon.hashCode);
    _$hash = $jc(_$hash, laborIndex.hashCode);
    _$hash = $jc(_$hash, materialIndex.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbRegion')
          ..add('id', id)
          ..add('name', name)
          ..add('type', type)
          ..add('parent', parent)
          ..add('centerLat', centerLat)
          ..add('centerLon', centerLon)
          ..add('laborIndex', laborIndex)
          ..add('materialIndex', materialIndex))
        .toString();
  }
}

class TbRegionBuilder implements Builder<TbRegion, TbRegionBuilder> {
  _$TbRegion? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  TbRegionTypeEnum? _type;
  TbRegionTypeEnum? get type => _$this._type;
  set type(TbRegionTypeEnum? type) => _$this._type = type;

  String? _parent;
  String? get parent => _$this._parent;
  set parent(String? parent) => _$this._parent = parent;

  double? _centerLat;
  double? get centerLat => _$this._centerLat;
  set centerLat(double? centerLat) => _$this._centerLat = centerLat;

  double? _centerLon;
  double? get centerLon => _$this._centerLon;
  set centerLon(double? centerLon) => _$this._centerLon = centerLon;

  int? _laborIndex;
  int? get laborIndex => _$this._laborIndex;
  set laborIndex(int? laborIndex) => _$this._laborIndex = laborIndex;

  int? _materialIndex;
  int? get materialIndex => _$this._materialIndex;
  set materialIndex(int? materialIndex) =>
      _$this._materialIndex = materialIndex;

  TbRegionBuilder() {
    TbRegion._defaults(this);
  }

  TbRegionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _type = $v.type;
      _parent = $v.parent;
      _centerLat = $v.centerLat;
      _centerLon = $v.centerLon;
      _laborIndex = $v.laborIndex;
      _materialIndex = $v.materialIndex;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbRegion other) {
    _$v = other as _$TbRegion;
  }

  @override
  void update(void Function(TbRegionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbRegion build() => _build();

  _$TbRegion _build() {
    final _$result = _$v ??
        _$TbRegion._(
          id: BuiltValueNullFieldError.checkNotNull(id, r'TbRegion', 'id'),
          name:
              BuiltValueNullFieldError.checkNotNull(name, r'TbRegion', 'name'),
          type:
              BuiltValueNullFieldError.checkNotNull(type, r'TbRegion', 'type'),
          parent: parent,
          centerLat: BuiltValueNullFieldError.checkNotNull(
              centerLat, r'TbRegion', 'centerLat'),
          centerLon: BuiltValueNullFieldError.checkNotNull(
              centerLon, r'TbRegion', 'centerLon'),
          laborIndex: laborIndex,
          materialIndex: materialIndex,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
