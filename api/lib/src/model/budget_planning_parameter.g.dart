// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget_planning_parameter.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BudgetPlanningParameter extends BudgetPlanningParameter {
  @override
  final String budgetId;
  @override
  final double area;
  @override
  final String region;
  @override
  final double? budgetAmount;
  @override
  final BuiltMap<String, String> attributeTypeValues;
  @override
  final BuiltMap<String, int> rooms;

  factory _$BudgetPlanningParameter(
          [void Function(BudgetPlanningParameterBuilder)? updates]) =>
      (BudgetPlanningParameterBuilder()..update(updates))._build();

  _$BudgetPlanningParameter._(
      {required this.budgetId,
      required this.area,
      required this.region,
      this.budgetAmount,
      required this.attributeTypeValues,
      required this.rooms})
      : super._();
  @override
  BudgetPlanningParameter rebuild(
          void Function(BudgetPlanningParameterBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BudgetPlanningParameterBuilder toBuilder() =>
      BudgetPlanningParameterBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BudgetPlanningParameter &&
        budgetId == other.budgetId &&
        area == other.area &&
        region == other.region &&
        budgetAmount == other.budgetAmount &&
        attributeTypeValues == other.attributeTypeValues &&
        rooms == other.rooms;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, budgetId.hashCode);
    _$hash = $jc(_$hash, area.hashCode);
    _$hash = $jc(_$hash, region.hashCode);
    _$hash = $jc(_$hash, budgetAmount.hashCode);
    _$hash = $jc(_$hash, attributeTypeValues.hashCode);
    _$hash = $jc(_$hash, rooms.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BudgetPlanningParameter')
          ..add('budgetId', budgetId)
          ..add('area', area)
          ..add('region', region)
          ..add('budgetAmount', budgetAmount)
          ..add('attributeTypeValues', attributeTypeValues)
          ..add('rooms', rooms))
        .toString();
  }
}

class BudgetPlanningParameterBuilder
    implements
        Builder<BudgetPlanningParameter, BudgetPlanningParameterBuilder> {
  _$BudgetPlanningParameter? _$v;

  String? _budgetId;
  String? get budgetId => _$this._budgetId;
  set budgetId(String? budgetId) => _$this._budgetId = budgetId;

  double? _area;
  double? get area => _$this._area;
  set area(double? area) => _$this._area = area;

  String? _region;
  String? get region => _$this._region;
  set region(String? region) => _$this._region = region;

  double? _budgetAmount;
  double? get budgetAmount => _$this._budgetAmount;
  set budgetAmount(double? budgetAmount) => _$this._budgetAmount = budgetAmount;

  MapBuilder<String, String>? _attributeTypeValues;
  MapBuilder<String, String> get attributeTypeValues =>
      _$this._attributeTypeValues ??= MapBuilder<String, String>();
  set attributeTypeValues(MapBuilder<String, String>? attributeTypeValues) =>
      _$this._attributeTypeValues = attributeTypeValues;

  MapBuilder<String, int>? _rooms;
  MapBuilder<String, int> get rooms =>
      _$this._rooms ??= MapBuilder<String, int>();
  set rooms(MapBuilder<String, int>? rooms) => _$this._rooms = rooms;

  BudgetPlanningParameterBuilder() {
    BudgetPlanningParameter._defaults(this);
  }

  BudgetPlanningParameterBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _budgetId = $v.budgetId;
      _area = $v.area;
      _region = $v.region;
      _budgetAmount = $v.budgetAmount;
      _attributeTypeValues = $v.attributeTypeValues.toBuilder();
      _rooms = $v.rooms.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BudgetPlanningParameter other) {
    _$v = other as _$BudgetPlanningParameter;
  }

  @override
  void update(void Function(BudgetPlanningParameterBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BudgetPlanningParameter build() => _build();

  _$BudgetPlanningParameter _build() {
    _$BudgetPlanningParameter _$result;
    try {
      _$result = _$v ??
          _$BudgetPlanningParameter._(
            budgetId: BuiltValueNullFieldError.checkNotNull(
                budgetId, r'BudgetPlanningParameter', 'budgetId'),
            area: BuiltValueNullFieldError.checkNotNull(
                area, r'BudgetPlanningParameter', 'area'),
            region: BuiltValueNullFieldError.checkNotNull(
                region, r'BudgetPlanningParameter', 'region'),
            budgetAmount: budgetAmount,
            attributeTypeValues: attributeTypeValues.build(),
            rooms: rooms.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'attributeTypeValues';
        attributeTypeValues.build();
        _$failedField = 'rooms';
        rooms.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BudgetPlanningParameter', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
