// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_budget_const.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbBudgetConst extends TbBudgetConst {
  @override
  final String budgetTypeId;
  @override
  final int? areaLaborFactor;
  @override
  final int? areaMaterialFactor;
  @override
  final int? fixedDeviceFactor;

  factory _$TbBudgetConst([void Function(TbBudgetConstBuilder)? updates]) =>
      (TbBudgetConstBuilder()..update(updates))._build();

  _$TbBudgetConst._(
      {required this.budgetTypeId,
      this.areaLaborFactor,
      this.areaMaterialFactor,
      this.fixedDeviceFactor})
      : super._();
  @override
  TbBudgetConst rebuild(void Function(TbBudgetConstBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbBudgetConstBuilder toBuilder() => TbBudgetConstBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbBudgetConst &&
        budgetTypeId == other.budgetTypeId &&
        areaLaborFactor == other.areaLaborFactor &&
        areaMaterialFactor == other.areaMaterialFactor &&
        fixedDeviceFactor == other.fixedDeviceFactor;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, budgetTypeId.hashCode);
    _$hash = $jc(_$hash, areaLaborFactor.hashCode);
    _$hash = $jc(_$hash, areaMaterialFactor.hashCode);
    _$hash = $jc(_$hash, fixedDeviceFactor.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbBudgetConst')
          ..add('budgetTypeId', budgetTypeId)
          ..add('areaLaborFactor', areaLaborFactor)
          ..add('areaMaterialFactor', areaMaterialFactor)
          ..add('fixedDeviceFactor', fixedDeviceFactor))
        .toString();
  }
}

class TbBudgetConstBuilder
    implements Builder<TbBudgetConst, TbBudgetConstBuilder> {
  _$TbBudgetConst? _$v;

  String? _budgetTypeId;
  String? get budgetTypeId => _$this._budgetTypeId;
  set budgetTypeId(String? budgetTypeId) => _$this._budgetTypeId = budgetTypeId;

  int? _areaLaborFactor;
  int? get areaLaborFactor => _$this._areaLaborFactor;
  set areaLaborFactor(int? areaLaborFactor) =>
      _$this._areaLaborFactor = areaLaborFactor;

  int? _areaMaterialFactor;
  int? get areaMaterialFactor => _$this._areaMaterialFactor;
  set areaMaterialFactor(int? areaMaterialFactor) =>
      _$this._areaMaterialFactor = areaMaterialFactor;

  int? _fixedDeviceFactor;
  int? get fixedDeviceFactor => _$this._fixedDeviceFactor;
  set fixedDeviceFactor(int? fixedDeviceFactor) =>
      _$this._fixedDeviceFactor = fixedDeviceFactor;

  TbBudgetConstBuilder() {
    TbBudgetConst._defaults(this);
  }

  TbBudgetConstBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _budgetTypeId = $v.budgetTypeId;
      _areaLaborFactor = $v.areaLaborFactor;
      _areaMaterialFactor = $v.areaMaterialFactor;
      _fixedDeviceFactor = $v.fixedDeviceFactor;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbBudgetConst other) {
    _$v = other as _$TbBudgetConst;
  }

  @override
  void update(void Function(TbBudgetConstBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbBudgetConst build() => _build();

  _$TbBudgetConst _build() {
    final _$result = _$v ??
        _$TbBudgetConst._(
          budgetTypeId: BuiltValueNullFieldError.checkNotNull(
              budgetTypeId, r'TbBudgetConst', 'budgetTypeId'),
          areaLaborFactor: areaLaborFactor,
          areaMaterialFactor: areaMaterialFactor,
          fixedDeviceFactor: fixedDeviceFactor,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
