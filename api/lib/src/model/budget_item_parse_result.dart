//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'budget_item_parse_result.g.dart';

/// BudgetItemParseResult
///
/// Properties:
/// * [title] 
/// * [lineAmount] 
/// * [url] 
/// * [platform] 
@BuiltValue()
abstract class BudgetItemParseResult implements Built<BudgetItemParseResult, BudgetItemParseResultBuilder> {
  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'lineAmount')
  double? get lineAmount;

  @BuiltValueField(wireName: r'url')
  String? get url;

  @BuiltValueField(wireName: r'platform')
  String? get platform;

  BudgetItemParseResult._();

  factory BudgetItemParseResult([void updates(BudgetItemParseResultBuilder b)]) = _$BudgetItemParseResult;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BudgetItemParseResultBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BudgetItemParseResult> get serializer => _$BudgetItemParseResultSerializer();
}

class _$BudgetItemParseResultSerializer implements PrimitiveSerializer<BudgetItemParseResult> {
  @override
  final Iterable<Type> types = const [BudgetItemParseResult, _$BudgetItemParseResult];

  @override
  final String wireName = r'BudgetItemParseResult';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BudgetItemParseResult object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType(String),
      );
    }
    if (object.lineAmount != null) {
      yield r'lineAmount';
      yield serializers.serialize(
        object.lineAmount,
        specifiedType: const FullType(double),
      );
    }
    if (object.url != null) {
      yield r'url';
      yield serializers.serialize(
        object.url,
        specifiedType: const FullType(String),
      );
    }
    if (object.platform != null) {
      yield r'platform';
      yield serializers.serialize(
        object.platform,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    BudgetItemParseResult object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BudgetItemParseResultBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.title = valueDes;
          break;
        case r'lineAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.lineAmount = valueDes;
          break;
        case r'url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.url = valueDes;
          break;
        case r'platform':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.platform = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BudgetItemParseResult deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BudgetItemParseResultBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

