// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upsert_budget_type_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpsertBudgetTypeRequest extends UpsertBudgetTypeRequest {
  @override
  final String id;
  @override
  final String name;
  @override
  final String? parentId;
  @override
  final int index;

  factory _$UpsertBudgetTypeRequest(
          [void Function(UpsertBudgetTypeRequestBuilder)? updates]) =>
      (UpsertBudgetTypeRequestBuilder()..update(updates))._build();

  _$UpsertBudgetTypeRequest._(
      {required this.id,
      required this.name,
      this.parentId,
      required this.index})
      : super._();
  @override
  UpsertBudgetTypeRequest rebuild(
          void Function(UpsertBudgetTypeRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpsertBudgetTypeRequestBuilder toBuilder() =>
      UpsertBudgetTypeRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpsertBudgetTypeRequest &&
        id == other.id &&
        name == other.name &&
        parentId == other.parentId &&
        index == other.index;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, index.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpsertBudgetTypeRequest')
          ..add('id', id)
          ..add('name', name)
          ..add('parentId', parentId)
          ..add('index', index))
        .toString();
  }
}

class UpsertBudgetTypeRequestBuilder
    implements
        Builder<UpsertBudgetTypeRequest, UpsertBudgetTypeRequestBuilder> {
  _$UpsertBudgetTypeRequest? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  int? _index;
  int? get index => _$this._index;
  set index(int? index) => _$this._index = index;

  UpsertBudgetTypeRequestBuilder() {
    UpsertBudgetTypeRequest._defaults(this);
  }

  UpsertBudgetTypeRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _parentId = $v.parentId;
      _index = $v.index;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpsertBudgetTypeRequest other) {
    _$v = other as _$UpsertBudgetTypeRequest;
  }

  @override
  void update(void Function(UpsertBudgetTypeRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpsertBudgetTypeRequest build() => _build();

  _$UpsertBudgetTypeRequest _build() {
    final _$result = _$v ??
        _$UpsertBudgetTypeRequest._(
          id: BuiltValueNullFieldError.checkNotNull(
              id, r'UpsertBudgetTypeRequest', 'id'),
          name: BuiltValueNullFieldError.checkNotNull(
              name, r'UpsertBudgetTypeRequest', 'name'),
          parentId: parentId,
          index: BuiltValueNullFieldError.checkNotNull(
              index, r'UpsertBudgetTypeRequest', 'index'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
