//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'budget_planning_item.g.dart';

/// BudgetPlanningItem
///
/// Properties:
/// * [name] 
/// * [type] 
/// * [budget] 
/// * [count] 
/// * [children] 
@BuiltValue()
abstract class BudgetPlanningItem implements Built<BudgetPlanningItem, BudgetPlanningItemBuilder> {
  @BuiltValueField(wireName: r'name')
  String get name;

  @BuiltValueField(wireName: r'type')
  String get type;

  @BuiltValueField(wireName: r'budget')
  double? get budget;

  @BuiltValueField(wireName: r'count')
  int? get count;

  @BuiltValueField(wireName: r'children')
  BuiltList<BudgetPlanningItem>? get children;

  BudgetPlanningItem._();

  factory BudgetPlanningItem([void updates(BudgetPlanningItemBuilder b)]) = _$BudgetPlanningItem;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BudgetPlanningItemBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BudgetPlanningItem> get serializer => _$BudgetPlanningItemSerializer();
}

class _$BudgetPlanningItemSerializer implements PrimitiveSerializer<BudgetPlanningItem> {
  @override
  final Iterable<Type> types = const [BudgetPlanningItem, _$BudgetPlanningItem];

  @override
  final String wireName = r'BudgetPlanningItem';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BudgetPlanningItem object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'name';
    yield serializers.serialize(
      object.name,
      specifiedType: const FullType(String),
    );
    yield r'type';
    yield serializers.serialize(
      object.type,
      specifiedType: const FullType(String),
    );
    if (object.budget != null) {
      yield r'budget';
      yield serializers.serialize(
        object.budget,
        specifiedType: const FullType(double),
      );
    }
    if (object.count != null) {
      yield r'count';
      yield serializers.serialize(
        object.count,
        specifiedType: const FullType(int),
      );
    }
    if (object.children != null) {
      yield r'children';
      yield serializers.serialize(
        object.children,
        specifiedType: const FullType(BuiltList, [FullType(BudgetPlanningItem)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    BudgetPlanningItem object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BudgetPlanningItemBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.type = valueDes;
          break;
        case r'budget':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(double),
          ) as double;
          result.budget = valueDes;
          break;
        case r'count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.count = valueDes;
          break;
        case r'children':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(BudgetPlanningItem)]),
          ) as BuiltList<BudgetPlanningItem>;
          result.children.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BudgetPlanningItem deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BudgetPlanningItemBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

