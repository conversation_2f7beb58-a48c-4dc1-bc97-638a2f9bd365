// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_user.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbUser extends TbUser {
  @override
  final String? id;
  @override
  final String name;
  @override
  final String? phone;
  @override
  final bool? isanonymous;

  factory _$TbUser([void Function(TbUserBuilder)? updates]) =>
      (TbUserBuilder()..update(updates))._build();

  _$TbUser._({this.id, required this.name, this.phone, this.isanonymous})
      : super._();
  @override
  TbUser rebuild(void Function(TbUserBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbUserBuilder toBuilder() => TbUserBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbUser &&
        id == other.id &&
        name == other.name &&
        phone == other.phone &&
        isanonymous == other.isanonymous;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, phone.hashCode);
    _$hash = $jc(_$hash, isanonymous.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbUser')
          ..add('id', id)
          ..add('name', name)
          ..add('phone', phone)
          ..add('isanonymous', isanonymous))
        .toString();
  }
}

class TbUserBuilder implements Builder<TbUser, TbUserBuilder> {
  _$TbUser? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _phone;
  String? get phone => _$this._phone;
  set phone(String? phone) => _$this._phone = phone;

  bool? _isanonymous;
  bool? get isanonymous => _$this._isanonymous;
  set isanonymous(bool? isanonymous) => _$this._isanonymous = isanonymous;

  TbUserBuilder() {
    TbUser._defaults(this);
  }

  TbUserBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _phone = $v.phone;
      _isanonymous = $v.isanonymous;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbUser other) {
    _$v = other as _$TbUser;
  }

  @override
  void update(void Function(TbUserBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbUser build() => _build();

  _$TbUser _build() {
    final _$result = _$v ??
        _$TbUser._(
          id: id,
          name: BuiltValueNullFieldError.checkNotNull(name, r'TbUser', 'name'),
          phone: phone,
          isanonymous: isanonymous,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
