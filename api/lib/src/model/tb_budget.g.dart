// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_budget.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbBudget extends TbBudget {
  @override
  final String? id;
  @override
  final String? projectId;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String createdByUser;
  @override
  final DateTime? createAt;
  @override
  final DateTime? updateAt;

  factory _$TbBudget([void Function(TbBudgetBuilder)? updates]) =>
      (TbBudgetBuilder()..update(updates))._build();

  _$TbBudget._(
      {this.id,
      this.projectId,
      required this.name,
      this.description,
      required this.createdByUser,
      this.createAt,
      this.updateAt})
      : super._();
  @override
  TbBudget rebuild(void Function(TbBudgetBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbBudgetBuilder toBuilder() => TbBudgetBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbBudget &&
        id == other.id &&
        projectId == other.projectId &&
        name == other.name &&
        description == other.description &&
        createdByUser == other.createdByUser &&
        createAt == other.createAt &&
        updateAt == other.updateAt;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, projectId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, createdByUser.hashCode);
    _$hash = $jc(_$hash, createAt.hashCode);
    _$hash = $jc(_$hash, updateAt.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbBudget')
          ..add('id', id)
          ..add('projectId', projectId)
          ..add('name', name)
          ..add('description', description)
          ..add('createdByUser', createdByUser)
          ..add('createAt', createAt)
          ..add('updateAt', updateAt))
        .toString();
  }
}

class TbBudgetBuilder implements Builder<TbBudget, TbBudgetBuilder> {
  _$TbBudget? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _projectId;
  String? get projectId => _$this._projectId;
  set projectId(String? projectId) => _$this._projectId = projectId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _createdByUser;
  String? get createdByUser => _$this._createdByUser;
  set createdByUser(String? createdByUser) =>
      _$this._createdByUser = createdByUser;

  DateTime? _createAt;
  DateTime? get createAt => _$this._createAt;
  set createAt(DateTime? createAt) => _$this._createAt = createAt;

  DateTime? _updateAt;
  DateTime? get updateAt => _$this._updateAt;
  set updateAt(DateTime? updateAt) => _$this._updateAt = updateAt;

  TbBudgetBuilder() {
    TbBudget._defaults(this);
  }

  TbBudgetBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _projectId = $v.projectId;
      _name = $v.name;
      _description = $v.description;
      _createdByUser = $v.createdByUser;
      _createAt = $v.createAt;
      _updateAt = $v.updateAt;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbBudget other) {
    _$v = other as _$TbBudget;
  }

  @override
  void update(void Function(TbBudgetBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbBudget build() => _build();

  _$TbBudget _build() {
    final _$result = _$v ??
        _$TbBudget._(
          id: id,
          projectId: projectId,
          name:
              BuiltValueNullFieldError.checkNotNull(name, r'TbBudget', 'name'),
          description: description,
          createdByUser: BuiltValueNullFieldError.checkNotNull(
              createdByUser, r'TbBudget', 'createdByUser'),
          createAt: createAt,
          updateAt: updateAt,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
