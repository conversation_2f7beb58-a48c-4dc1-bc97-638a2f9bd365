// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_task.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbTask extends TbTask {
  @override
  final String title;
  @override
  final String assignToRole;
  @override
  final DateTime? createTime;
  @override
  final DateTime? targetTime;
  @override
  final DateTime? dueTime;
  @override
  final String groupId;
  @override
  final String createUser;
  @override
  final String? planId;
  @override
  final JsonObject? form;

  factory _$TbTask([void Function(TbTaskBuilder)? updates]) =>
      (TbTaskBuilder()..update(updates))._build();

  _$TbTask._(
      {required this.title,
      required this.assignToRole,
      this.createTime,
      this.targetTime,
      this.dueTime,
      required this.groupId,
      required this.createUser,
      this.planId,
      this.form})
      : super._();
  @override
  TbTask rebuild(void Function(TbTaskBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbTaskBuilder toBuilder() => TbTaskBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbTask &&
        title == other.title &&
        assignToRole == other.assignToRole &&
        createTime == other.createTime &&
        targetTime == other.targetTime &&
        dueTime == other.dueTime &&
        groupId == other.groupId &&
        createUser == other.createUser &&
        planId == other.planId &&
        form == other.form;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, assignToRole.hashCode);
    _$hash = $jc(_$hash, createTime.hashCode);
    _$hash = $jc(_$hash, targetTime.hashCode);
    _$hash = $jc(_$hash, dueTime.hashCode);
    _$hash = $jc(_$hash, groupId.hashCode);
    _$hash = $jc(_$hash, createUser.hashCode);
    _$hash = $jc(_$hash, planId.hashCode);
    _$hash = $jc(_$hash, form.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbTask')
          ..add('title', title)
          ..add('assignToRole', assignToRole)
          ..add('createTime', createTime)
          ..add('targetTime', targetTime)
          ..add('dueTime', dueTime)
          ..add('groupId', groupId)
          ..add('createUser', createUser)
          ..add('planId', planId)
          ..add('form', form))
        .toString();
  }
}

class TbTaskBuilder implements Builder<TbTask, TbTaskBuilder> {
  _$TbTask? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _assignToRole;
  String? get assignToRole => _$this._assignToRole;
  set assignToRole(String? assignToRole) => _$this._assignToRole = assignToRole;

  DateTime? _createTime;
  DateTime? get createTime => _$this._createTime;
  set createTime(DateTime? createTime) => _$this._createTime = createTime;

  DateTime? _targetTime;
  DateTime? get targetTime => _$this._targetTime;
  set targetTime(DateTime? targetTime) => _$this._targetTime = targetTime;

  DateTime? _dueTime;
  DateTime? get dueTime => _$this._dueTime;
  set dueTime(DateTime? dueTime) => _$this._dueTime = dueTime;

  String? _groupId;
  String? get groupId => _$this._groupId;
  set groupId(String? groupId) => _$this._groupId = groupId;

  String? _createUser;
  String? get createUser => _$this._createUser;
  set createUser(String? createUser) => _$this._createUser = createUser;

  String? _planId;
  String? get planId => _$this._planId;
  set planId(String? planId) => _$this._planId = planId;

  JsonObject? _form;
  JsonObject? get form => _$this._form;
  set form(JsonObject? form) => _$this._form = form;

  TbTaskBuilder() {
    TbTask._defaults(this);
  }

  TbTaskBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _assignToRole = $v.assignToRole;
      _createTime = $v.createTime;
      _targetTime = $v.targetTime;
      _dueTime = $v.dueTime;
      _groupId = $v.groupId;
      _createUser = $v.createUser;
      _planId = $v.planId;
      _form = $v.form;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbTask other) {
    _$v = other as _$TbTask;
  }

  @override
  void update(void Function(TbTaskBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbTask build() => _build();

  _$TbTask _build() {
    final _$result = _$v ??
        _$TbTask._(
          title:
              BuiltValueNullFieldError.checkNotNull(title, r'TbTask', 'title'),
          assignToRole: BuiltValueNullFieldError.checkNotNull(
              assignToRole, r'TbTask', 'assignToRole'),
          createTime: createTime,
          targetTime: targetTime,
          dueTime: dueTime,
          groupId: BuiltValueNullFieldError.checkNotNull(
              groupId, r'TbTask', 'groupId'),
          createUser: BuiltValueNullFieldError.checkNotNull(
              createUser, r'TbTask', 'createUser'),
          planId: planId,
          form: form,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
