// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_budget_room_const.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbBudgetRoomConst extends TbBudgetRoomConst {
  @override
  final String budgetTypeId;
  @override
  final String roomId;
  @override
  final int? roomLaborFactor;
  @override
  final int? roomMaterialFactor;

  factory _$TbBudgetRoomConst(
          [void Function(TbBudgetRoomConstBuilder)? updates]) =>
      (TbBudgetRoomConstBuilder()..update(updates))._build();

  _$TbBudgetRoomConst._(
      {required this.budgetTypeId,
      required this.roomId,
      this.roomLaborFactor,
      this.roomMaterialFactor})
      : super._();
  @override
  TbBudgetRoomConst rebuild(void Function(TbBudgetRoomConstBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbBudgetRoomConstBuilder toBuilder() =>
      TbBudgetRoomConstBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbBudgetRoomConst &&
        budgetTypeId == other.budgetTypeId &&
        roomId == other.roomId &&
        roomLaborFactor == other.roomLaborFactor &&
        roomMaterialFactor == other.roomMaterialFactor;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, budgetTypeId.hashCode);
    _$hash = $jc(_$hash, roomId.hashCode);
    _$hash = $jc(_$hash, roomLaborFactor.hashCode);
    _$hash = $jc(_$hash, roomMaterialFactor.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbBudgetRoomConst')
          ..add('budgetTypeId', budgetTypeId)
          ..add('roomId', roomId)
          ..add('roomLaborFactor', roomLaborFactor)
          ..add('roomMaterialFactor', roomMaterialFactor))
        .toString();
  }
}

class TbBudgetRoomConstBuilder
    implements Builder<TbBudgetRoomConst, TbBudgetRoomConstBuilder> {
  _$TbBudgetRoomConst? _$v;

  String? _budgetTypeId;
  String? get budgetTypeId => _$this._budgetTypeId;
  set budgetTypeId(String? budgetTypeId) => _$this._budgetTypeId = budgetTypeId;

  String? _roomId;
  String? get roomId => _$this._roomId;
  set roomId(String? roomId) => _$this._roomId = roomId;

  int? _roomLaborFactor;
  int? get roomLaborFactor => _$this._roomLaborFactor;
  set roomLaborFactor(int? roomLaborFactor) =>
      _$this._roomLaborFactor = roomLaborFactor;

  int? _roomMaterialFactor;
  int? get roomMaterialFactor => _$this._roomMaterialFactor;
  set roomMaterialFactor(int? roomMaterialFactor) =>
      _$this._roomMaterialFactor = roomMaterialFactor;

  TbBudgetRoomConstBuilder() {
    TbBudgetRoomConst._defaults(this);
  }

  TbBudgetRoomConstBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _budgetTypeId = $v.budgetTypeId;
      _roomId = $v.roomId;
      _roomLaborFactor = $v.roomLaborFactor;
      _roomMaterialFactor = $v.roomMaterialFactor;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbBudgetRoomConst other) {
    _$v = other as _$TbBudgetRoomConst;
  }

  @override
  void update(void Function(TbBudgetRoomConstBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbBudgetRoomConst build() => _build();

  _$TbBudgetRoomConst _build() {
    final _$result = _$v ??
        _$TbBudgetRoomConst._(
          budgetTypeId: BuiltValueNullFieldError.checkNotNull(
              budgetTypeId, r'TbBudgetRoomConst', 'budgetTypeId'),
          roomId: BuiltValueNullFieldError.checkNotNull(
              roomId, r'TbBudgetRoomConst', 'roomId'),
          roomLaborFactor: roomLaborFactor,
          roomMaterialFactor: roomMaterialFactor,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
