// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_room_type.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TbRoomType extends TbRoomType {
  @override
  final String? id;
  @override
  final String name;
  @override
  final int? index;
  @override
  final bool? defaultVisiable;
  @override
  final String shortName;
  @override
  final int? defaultCount;

  factory _$TbRoomType([void Function(TbRoomTypeBuilder)? updates]) =>
      (TbRoomTypeBuilder()..update(updates))._build();

  _$TbRoomType._(
      {this.id,
      required this.name,
      this.index,
      this.defaultVisiable,
      required this.shortName,
      this.defaultCount})
      : super._();
  @override
  TbRoomType rebuild(void Function(TbRoomTypeBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TbRoomTypeBuilder toBuilder() => TbRoomTypeBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TbRoomType &&
        id == other.id &&
        name == other.name &&
        index == other.index &&
        defaultVisiable == other.defaultVisiable &&
        shortName == other.shortName &&
        defaultCount == other.defaultCount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, index.hashCode);
    _$hash = $jc(_$hash, defaultVisiable.hashCode);
    _$hash = $jc(_$hash, shortName.hashCode);
    _$hash = $jc(_$hash, defaultCount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TbRoomType')
          ..add('id', id)
          ..add('name', name)
          ..add('index', index)
          ..add('defaultVisiable', defaultVisiable)
          ..add('shortName', shortName)
          ..add('defaultCount', defaultCount))
        .toString();
  }
}

class TbRoomTypeBuilder implements Builder<TbRoomType, TbRoomTypeBuilder> {
  _$TbRoomType? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  int? _index;
  int? get index => _$this._index;
  set index(int? index) => _$this._index = index;

  bool? _defaultVisiable;
  bool? get defaultVisiable => _$this._defaultVisiable;
  set defaultVisiable(bool? defaultVisiable) =>
      _$this._defaultVisiable = defaultVisiable;

  String? _shortName;
  String? get shortName => _$this._shortName;
  set shortName(String? shortName) => _$this._shortName = shortName;

  int? _defaultCount;
  int? get defaultCount => _$this._defaultCount;
  set defaultCount(int? defaultCount) => _$this._defaultCount = defaultCount;

  TbRoomTypeBuilder() {
    TbRoomType._defaults(this);
  }

  TbRoomTypeBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _index = $v.index;
      _defaultVisiable = $v.defaultVisiable;
      _shortName = $v.shortName;
      _defaultCount = $v.defaultCount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TbRoomType other) {
    _$v = other as _$TbRoomType;
  }

  @override
  void update(void Function(TbRoomTypeBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TbRoomType build() => _build();

  _$TbRoomType _build() {
    final _$result = _$v ??
        _$TbRoomType._(
          id: id,
          name: BuiltValueNullFieldError.checkNotNull(
              name, r'TbRoomType', 'name'),
          index: index,
          defaultVisiable: defaultVisiable,
          shortName: BuiltValueNullFieldError.checkNotNull(
              shortName, r'TbRoomType', 'shortName'),
          defaultCount: defaultCount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
