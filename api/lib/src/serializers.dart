//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:openapi/src/date_serializer.dart';
import 'package:openapi/src/model/date.dart';

import 'package:openapi/src/model/budget_item_parse_result.dart';
import 'package:openapi/src/model/budget_payment.dart';
import 'package:openapi/src/model/budget_planning_item.dart';
import 'package:openapi/src/model/budget_planning_parameter.dart';
import 'package:openapi/src/model/create_budget_request.dart';
import 'package:openapi/src/model/create_relation.dart';
import 'package:openapi/src/model/create_response_string.dart';
import 'package:openapi/src/model/create_task_request.dart';
import 'package:openapi/src/model/location.dart';
import 'package:openapi/src/model/login_response.dart';
import 'package:openapi/src/model/rl_user_project.dart';
import 'package:openapi/src/model/send_code_response.dart';
import 'package:openapi/src/model/sms_request.dart';
import 'package:openapi/src/model/sms_verify_request.dart';
import 'package:openapi/src/model/tb_budget.dart';
import 'package:openapi/src/model/tb_budget_const.dart';
import 'package:openapi/src/model/tb_budget_factor.dart';
import 'package:openapi/src/model/tb_budget_item.dart';
import 'package:openapi/src/model/tb_budget_room_const.dart';
import 'package:openapi/src/model/tb_budget_type.dart';
import 'package:openapi/src/model/tb_house_attribute.dart';
import 'package:openapi/src/model/tb_house_attribute_type.dart';
import 'package:openapi/src/model/tb_plan_item.dart';
import 'package:openapi/src/model/tb_project.dart';
import 'package:openapi/src/model/tb_region.dart';
import 'package:openapi/src/model/tb_role.dart';
import 'package:openapi/src/model/tb_room_type.dart';
import 'package:openapi/src/model/tb_task.dart';
import 'package:openapi/src/model/tb_user.dart';
import 'package:openapi/src/model/upsert_budget_type_request.dart';
import 'package:openapi/src/model/upsert_project_request.dart';

part 'serializers.g.dart';

@SerializersFor([
  BudgetItemParseResult,
  BudgetPayment,
  BudgetPlanningItem,
  BudgetPlanningParameter,
  CreateBudgetRequest,
  CreateRelation,
  CreateResponseString,
  CreateTaskRequest,
  Location,
  LoginResponse,
  RlUserProject,
  SendCodeResponse,
  SmsRequest,
  SmsVerifyRequest,
  TbBudget,
  TbBudgetConst,
  TbBudgetFactor,
  TbBudgetItem,
  TbBudgetRoomConst,
  TbBudgetType,
  TbHouseAttribute,
  TbHouseAttributeType,
  TbPlanItem,
  TbProject,
  TbRegion,
  TbRole,
  TbRoomType,
  TbTask,
  TbUser,
  UpsertBudgetTypeRequest,
  UpsertProjectRequest,
])
Serializers serializers = (_$serializers.toBuilder()
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbBudgetItem)]),
        () => ListBuilder<TbBudgetItem>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbBudgetRoomConst)]),
        () => ListBuilder<TbBudgetRoomConst>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbBudgetFactor)]),
        () => ListBuilder<TbBudgetFactor>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbBudgetType)]),
        () => ListBuilder<TbBudgetType>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbRole)]),
        () => ListBuilder<TbRole>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbHouseAttributeType)]),
        () => ListBuilder<TbHouseAttributeType>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(Location)]),
        () => ListBuilder<Location>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbProject)]),
        () => ListBuilder<TbProject>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbHouseAttribute)]),
        () => ListBuilder<TbHouseAttribute>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbBudgetConst)]),
        () => ListBuilder<TbBudgetConst>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbTask)]),
        () => ListBuilder<TbTask>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbBudget)]),
        () => ListBuilder<TbBudget>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(RlUserProject)]),
        () => ListBuilder<RlUserProject>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbRoomType)]),
        () => ListBuilder<TbRoomType>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbUser)]),
        () => ListBuilder<TbUser>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbRegion)]),
        () => ListBuilder<TbRegion>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(String)]),
        () => ListBuilder<String>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(TbPlanItem)]),
        () => ListBuilder<TbPlanItem>(),
      )
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer())
    ).build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
