// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

Serializers _$serializers = (Serializers().toBuilder()
      ..add(BudgetItemParseResult.serializer)
      ..add(BudgetPayment.serializer)
      ..add(BudgetPlanningItem.serializer)
      ..add(BudgetPlanningParameter.serializer)
      ..add(CreateBudgetRequest.serializer)
      ..add(CreateRelation.serializer)
      ..add(CreateResponseString.serializer)
      ..add(CreateTaskRequest.serializer)
      ..add(Location.serializer)
      ..add(LoginResponse.serializer)
      ..add(RlUserProject.serializer)
      ..add(SendCodeResponse.serializer)
      ..add(SmsRequest.serializer)
      ..add(SmsVerifyRequest.serializer)
      ..add(TbBudget.serializer)
      ..add(TbBudgetConst.serializer)
      ..add(TbBudgetFactor.serializer)
      ..add(TbBudgetItem.serializer)
      ..add(TbBudgetRoomConst.serializer)
      ..add(TbBudgetType.serializer)
      ..add(TbHouseAttribute.serializer)
      ..add(TbHouseAttributeType.serializer)
      ..add(TbPlanItem.serializer)
      ..add(TbProject.serializer)
      ..add(TbRegion.serializer)
      ..add(TbRegionTypeEnum.serializer)
      ..add(TbRole.serializer)
      ..add(TbRoomType.serializer)
      ..add(TbTask.serializer)
      ..add(TbUser.serializer)
      ..add(UpsertBudgetTypeRequest.serializer)
      ..add(UpsertProjectRequest.serializer)
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(BudgetPlanningItem)]),
          () => ListBuilder<BudgetPlanningItem>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(
              BuiltMap, const [const FullType(String), const FullType(String)]),
          () => MapBuilder<String, String>())
      ..addBuilderFactory(
          const FullType(
              BuiltMap, const [const FullType(String), const FullType(int)]),
          () => MapBuilder<String, int>()))
    .build();

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
