import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for Location
void main() {
  final instance = LocationBuilder();
  // TODO add properties to the builder and call build()

  group(Location, () {
    // String inputAddress
    test('to test the property `inputAddress`', () async {
      // TODO
    });

    // String fullAddress
    test('to test the property `fullAddress`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // double latitude
    test('to test the property `latitude`', () async {
      // TODO
    });

    // double longitude
    test('to test the property `longitude`', () async {
      // TODO
    });

  });
}
