import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbRoomType
void main() {
  final instance = TbRoomTypeBuilder();
  // TODO add properties to the builder and call build()

  group(TbRoomType, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // int index
    test('to test the property `index`', () async {
      // TODO
    });

    // bool defaultVisiable
    test('to test the property `defaultVisiable`', () async {
      // TODO
    });

    // String shortName
    test('to test the property `shortName`', () async {
      // TODO
    });

    // int defaultCount
    test('to test the property `defaultCount`', () async {
      // TODO
    });

  });
}
