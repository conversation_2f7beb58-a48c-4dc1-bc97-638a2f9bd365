import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for BudgetApi
void main() {
  final instance = Openapi().getBudgetApi();

  group(BudgetApi, () {
    // 创建预算管理清单
    //
    //Future<String> createBudget(CreateBudgetRequest createBudgetRequest) async
    test('test createBudget', () async {
      // TODO
    });

    // 删除预算清单
    //
    //Future<bool> deleteBudget(String id) async
    test('test deleteBudget', () async {
      // TODO
    });

    // 删除预算项
    //
    //Future<bool> deleteItem(BuiltList<String> ids) async
    test('test deleteItem', () async {
      // TODO
    });

    // 获取预算项, 将payment_record转换为`BudgetPayment`
    //
    //Future<BuiltList<TbBudgetItem>> getBudgetItems(String budgetId) async
    test('test getBudgetItems', () async {
      // TODO
    });

    // 获取预算清单
    //
    //Future<BuiltList<TbBudget>> getBudgetsByUser() async
    test('test getBudgetsByUser', () async {
      // TODO
    });

    //Future<TbBudget> getByProjectID(String projectId) async
    test('test getByProjectID', () async {
      // TODO
    });

    //Future<TbBudget> getDefaultBudget() async
    test('test getDefaultBudget', () async {
      // TODO
    });

    // 生成客户端BudgetPayment原型，无实际作用
    //
    //Future<BudgetPayment> linkBudgetPayment() async
    test('test linkBudgetPayment', () async {
      // TODO
    });

    // 解析图片信息
    //
    // 从图片中解析出商品信息
    //
    //Future<BudgetItemParseResult> parseImage(MultipartFile file) async
    test('test parseImage', () async {
      // TODO
    });

    // 解析分享链接
    //
    // 从分享链接中解析出商品信息
    //
    //Future<BudgetItemParseResult> parseShareURL(String body) async
    test('test parseShareURL', () async {
      // TODO
    });

    // 创建预算项
    //
    //Future<bool> upsertItem(BuiltList<TbBudgetItem> tbBudgetItem) async
    test('test upsertItem', () async {
      // TODO
    });

  });
}
