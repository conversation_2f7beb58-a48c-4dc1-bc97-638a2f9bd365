import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for BudgetTypeApi
void main() {
  final instance = Openapi().getBudgetTypeApi();

  group(BudgetTypeApi, () {
    // 删除预算项类型
    //
    //Future<bool> deleteBudgetType(String id) async
    test('test deleteBudgetType', () async {
      // TODO
    });

    //Future<BuiltList<TbBudgetType>> getBudgetTypes() async
    test('test getBudgetTypes', () async {
      // TODO
    });

    //Future<bool> upsertBudgetType(UpsertBudgetTypeRequest upsertBudgetTypeRequest) async
    test('test upsertBudgetType', () async {
      // TODO
    });

  });
}
