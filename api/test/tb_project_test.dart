import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbProject
void main() {
  final instance = TbProjectBuilder();
  // TODO add properties to the builder and call build()

  group(TbProject, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // String address
    test('to test the property `address`', () async {
      // TODO
    });

    // double latitude
    test('to test the property `latitude`', () async {
      // TODO
    });

    // double longitude
    test('to test the property `longitude`', () async {
      // TODO
    });

    // double area
    test('to test the property `area`', () async {
      // TODO
    });

    // String style
    test('to test the property `style`', () async {
      // TODO
    });

    // int bedrooms
    test('to test the property `bedrooms`', () async {
      // TODO
    });

    // int livingrooms
    test('to test the property `livingrooms`', () async {
      // TODO
    });

    // int bathrooms
    test('to test the property `bathrooms`', () async {
      // TODO
    });

    // int kitchens
    test('to test the property `kitchens`', () async {
      // TODO
    });

    // int budget
    test('to test the property `budget`', () async {
      // TODO
    });

    // DateTime startDate
    test('to test the property `startDate`', () async {
      // TODO
    });

  });
}
