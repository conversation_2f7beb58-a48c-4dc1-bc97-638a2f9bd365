import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for CreateResponseString
void main() {
  final instance = CreateResponseStringBuilder();
  // TODO add properties to the builder and call build()

  group(CreateResponseString, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // String id
    test('to test the property `id`', () async {
      // TODO
    });

  });
}
