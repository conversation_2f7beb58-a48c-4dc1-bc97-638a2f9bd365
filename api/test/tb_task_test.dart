import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbTask
void main() {
  final instance = TbTaskBuilder();
  // TODO add properties to the builder and call build()

  group(TbTask, () {
    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // String assignToRole
    test('to test the property `assignToRole`', () async {
      // TODO
    });

    // DateTime createTime
    test('to test the property `createTime`', () async {
      // TODO
    });

    // DateTime targetTime
    test('to test the property `targetTime`', () async {
      // TODO
    });

    // DateTime dueTime
    test('to test the property `dueTime`', () async {
      // TODO
    });

    // String groupId
    test('to test the property `groupId`', () async {
      // TODO
    });

    // String createUser
    test('to test the property `createUser`', () async {
      // TODO
    });

    // String planId
    test('to test the property `planId`', () async {
      // TODO
    });

    // JsonObject form
    test('to test the property `form`', () async {
      // TODO
    });

  });
}
