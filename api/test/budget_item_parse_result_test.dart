import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for BudgetItemParseResult
void main() {
  final instance = BudgetItemParseResultBuilder();
  // TODO add properties to the builder and call build()

  group(BudgetItemParseResult, () {
    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // double lineAmount
    test('to test the property `lineAmount`', () async {
      // TODO
    });

    // String url
    test('to test the property `url`', () async {
      // TODO
    });

    // String platform
    test('to test the property `platform`', () async {
      // TODO
    });

  });
}
