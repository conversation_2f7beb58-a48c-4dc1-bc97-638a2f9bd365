import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbUser
void main() {
  final instance = TbUserBuilder();
  // TODO add properties to the builder and call build()

  group(TbUser, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String phone
    test('to test the property `phone`', () async {
      // TODO
    });

    // bool isanonymous
    test('to test the property `isanonymous`', () async {
      // TODO
    });

  });
}
