import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for RlUserProject
void main() {
  final instance = RlUserProjectBuilder();
  // TODO add properties to the builder and call build()

  group(RlUserProject, () {
    // String userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // String projectId
    test('to test the property `projectId`', () async {
      // TODO
    });

    // bool favorite
    test('to test the property `favorite`', () async {
      // TODO
    });

    // bool disabled
    test('to test the property `disabled`', () async {
      // TODO
    });

    // String role
    test('to test the property `role`', () async {
      // TODO
    });

    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String userDisplayName
    test('to test the property `userDisplayName`', () async {
      // TODO
    });

  });
}
