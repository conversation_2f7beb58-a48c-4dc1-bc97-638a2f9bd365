import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbRegion
void main() {
  final instance = TbRegionBuilder();
  // TODO add properties to the builder and call build()

  group(TbRegion, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String type
    test('to test the property `type`', () async {
      // TODO
    });

    // String parent
    test('to test the property `parent`', () async {
      // TODO
    });

    // double centerLat
    test('to test the property `centerLat`', () async {
      // TODO
    });

    // double centerLon
    test('to test the property `centerLon`', () async {
      // TODO
    });

    // int laborIndex
    test('to test the property `laborIndex`', () async {
      // TODO
    });

    // int materialIndex
    test('to test the property `materialIndex`', () async {
      // TODO
    });

  });
}
