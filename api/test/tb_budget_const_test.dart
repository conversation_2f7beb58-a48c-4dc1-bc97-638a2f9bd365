import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbBudgetConst
void main() {
  final instance = TbBudgetConstBuilder();
  // TODO add properties to the builder and call build()

  group(TbBudgetConst, () {
    // String budgetTypeId
    test('to test the property `budgetTypeId`', () async {
      // TODO
    });

    // int areaLaborFactor
    test('to test the property `areaLaborFactor`', () async {
      // TODO
    });

    // int areaMaterialFactor
    test('to test the property `areaMaterialFactor`', () async {
      // TODO
    });

    // int fixedDeviceFactor
    test('to test the property `fixedDeviceFactor`', () async {
      // TODO
    });

  });
}
