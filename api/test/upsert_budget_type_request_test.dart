import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for UpsertBudgetTypeRequest
void main() {
  final instance = UpsertBudgetTypeRequestBuilder();
  // TODO add properties to the builder and call build()

  group(UpsertBudgetTypeRequest, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String parentId
    test('to test the property `parentId`', () async {
      // TODO
    });

    // int index
    test('to test the property `index`', () async {
      // TODO
    });

  });
}
