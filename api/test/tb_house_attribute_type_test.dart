import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbHouseAttributeType
void main() {
  final instance = TbHouseAttributeTypeBuilder();
  // TODO add properties to the builder and call build()

  group(TbHouseAttributeType, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // int index
    test('to test the property `index`', () async {
      // TODO
    });

    // bool defaultVisiable
    test('to test the property `defaultVisiable`', () async {
      // TODO
    });

  });
}
