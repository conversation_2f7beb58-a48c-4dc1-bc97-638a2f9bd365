import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for UserControllerApi
void main() {
  final instance = Openapi().getUserControllerApi();

  group(UserControllerApi, () {
    //Future<TbUser> getCurrentUser() async
    test('test getCurrentUser', () async {
      // TODO
    });

    //Future<BuiltList<TbUser>> getUsers1(BuiltList<String> userIds) async
    test('test getUsers1', () async {
      // TODO
    });

  });
}
