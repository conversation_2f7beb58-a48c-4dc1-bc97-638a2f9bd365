import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for UpsertProjectRequest
void main() {
  final instance = UpsertProjectRequestBuilder();
  // TODO add properties to the builder and call build()

  group(UpsertProjectRequest, () {
    // TbProject project
    test('to test the property `project`', () async {
      // TODO
    });

    // String creatorName
    test('to test the property `creatorName`', () async {
      // TODO
    });

    // BuiltList<String> creatorRoles
    test('to test the property `creatorRoles`', () async {
      // TODO
    });

  });
}
