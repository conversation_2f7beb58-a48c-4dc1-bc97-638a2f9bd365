import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for SmsApi
void main() {
  final instance = Openapi().getSmsApi();

  group(SmsApi, () {
    //Future callGet() async
    test('test callGet', () async {
      // TODO
    });

    // 短信登录
    //
    // 使用手机号和验证码登录并获取JWT令牌
    //
    //Future<LoginResponse> login(SmsVerifyRequest smsVerifyRequest) async
    test('test login', () async {
      // TODO
    });

    // 发送验证码
    //
    // 向指定手机号发送登录验证码
    //
    //Future<SendCodeResponse> sendCode(SmsRequest smsRequest) async
    test('test sendCode', () async {
      // TODO
    });

  });
}
