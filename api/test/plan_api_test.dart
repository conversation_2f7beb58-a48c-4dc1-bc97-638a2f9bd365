import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for PlanApi
void main() {
  final instance = Openapi().getPlanApi();

  group(PlanApi, () {
    //Future<bool> deletePlan(String id) async
    test('test deletePlan', () async {
      // TODO
    });

    //Future<BuiltList<TbPlanItem>> getPlan(String projectId) async
    test('test getPlan', () async {
      // TODO
    });

    //Future<bool> upsertPlan(String projectId, TbPlanItem tbPlanItem) async
    test('test upsertPlan', () async {
      // TODO
    });

  });
}
