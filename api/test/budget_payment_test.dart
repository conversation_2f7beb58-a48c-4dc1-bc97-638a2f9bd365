import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for BudgetPayment
void main() {
  final instance = BudgetPaymentBuilder();
  // TODO add properties to the builder and call build()

  group(BudgetPayment, () {
    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // double amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // DateTime time
    test('to test the property `time`', () async {
      // TODO
    });

  });
}
