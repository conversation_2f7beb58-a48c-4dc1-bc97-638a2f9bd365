import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for CreateTaskRequest
void main() {
  final instance = CreateTaskRequestBuilder();
  // TODO add properties to the builder and call build()

  group(CreateTaskRequest, () {
    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // String groupId
    test('to test the property `groupId`', () async {
      // TODO
    });

    // String planId
    test('to test the property `planId`', () async {
      // TODO
    });

    // BuiltList<String> assignToRoles
    test('to test the property `assignToRoles`', () async {
      // TODO
    });

    // bool confirmRequired
    test('to test the property `confirmRequired`', () async {
      // TODO
    });

    // BuiltList<String> messages
    test('to test the property `messages`', () async {
      // TODO
    });

    // BuiltList<String> attachments
    test('to test the property `attachments`', () async {
      // TODO
    });

  });
}
