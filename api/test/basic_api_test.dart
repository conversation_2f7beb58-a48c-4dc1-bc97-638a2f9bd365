import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for BasicApi
void main() {
  final instance = Openapi().getBasicApi();

  group(BasicApi, () {
    //Future<BuiltList<TbHouseAttributeType>> getHouseAttributeTypes() async
    test('test getHouseAttributeTypes', () async {
      // TODO
    });

    //Future<BuiltList<TbHouseAttribute>> getHouseAttributes() async
    test('test getHouseAttributes', () async {
      // TODO
    });

    //Future<BuiltList<TbRoomType>> getRoomTypes() async
    test('test getRoomTypes', () async {
      // TODO
    });

  });
}
