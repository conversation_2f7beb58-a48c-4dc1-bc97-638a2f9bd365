import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for CreateBudgetRequest
void main() {
  final instance = CreateBudgetRequestBuilder();
  // TODO add properties to the builder and call build()

  group(CreateBudgetRequest, () {
    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String projectId
    test('to test the property `projectId`', () async {
      // TODO
    });

  });
}
