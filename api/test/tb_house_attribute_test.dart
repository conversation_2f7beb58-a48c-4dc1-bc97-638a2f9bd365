import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbHouseAttribute
void main() {
  final instance = TbHouseAttributeBuilder();
  // TODO add properties to the builder and call build()

  group(TbHouseAttribute, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String type
    test('to test the property `type`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // int index
    test('to test the property `index`', () async {
      // TODO
    });

    // int defaultFactor
    test('to test the property `defaultFactor`', () async {
      // TODO
    });

    // bool isDefault
    test('to test the property `isDefault`', () async {
      // TODO
    });

    // String imageUrl
    test('to test the property `imageUrl`', () async {
      // TODO
    });

  });
}
