import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for SendCodeResponse
void main() {
  final instance = SendCodeResponseBuilder();
  // TODO add properties to the builder and call build()

  group(SendCodeResponse, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // int expire
    test('to test the property `expire`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });

  });
}
