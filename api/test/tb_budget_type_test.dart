import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbBudgetType
void main() {
  final instance = TbBudgetTypeBuilder();
  // TODO add properties to the builder and call build()

  group(TbBudgetType, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String parent
    test('to test the property `parent`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // int index
    test('to test the property `index`', () async {
      // TODO
    });

    // bool planable
    test('to test the property `planable`', () async {
      // TODO
    });

  });
}
