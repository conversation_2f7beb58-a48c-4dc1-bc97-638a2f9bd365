import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbBudgetFactor
void main() {
  final instance = TbBudgetFactorBuilder();
  // TODO add properties to the builder and call build()

  group(TbBudgetFactor, () {
    // String budgetTypeId
    test('to test the property `budgetTypeId`', () async {
      // TODO
    });

    // String attribute
    test('to test the property `attribute`', () async {
      // TODO
    });

    // int value
    test('to test the property `value`', () async {
      // TODO
    });

    // String key
    test('to test the property `key`', () async {
      // TODO
    });

  });
}
