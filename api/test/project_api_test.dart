import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for ProjectApi
void main() {
  final instance = Openapi().getProjectApi();

  group(ProjectApi, () {
    //Future<bool> createRelation(CreateRelation createRelation) async
    test('test createRelation', () async {
      // TODO
    });

    //Future<bool> deleteProject(String projectId) async
    test('test deleteProject', () async {
      // TODO
    });

    //Future<bool> deleteRelation(String projectId, String userId, { String roleId }) async
    test('test deleteRelation', () async {
      // TODO
    });

    //Future<bool> deleteRole(String projectId, String roleId) async
    test('test deleteRole', () async {
      // TODO
    });

    //Future<BuiltList<RlUserProject>> getMembers(String projectId) async
    test('test getMembers', () async {
      // TODO
    });

    //Future<TbProject> getProject(String id) async
    test('test getProject', () async {
      // TODO
    });

    //Future<BuiltList<TbProject>> getProjects() async
    test('test getProjects', () async {
      // TODO
    });

    //Future<BuiltList<TbRole>> getRoles({ String projectId }) async
    test('test getRoles', () async {
      // TODO
    });

    //Future<CreateResponseString> upsertProject(UpsertProjectRequest upsertProjectRequest) async
    test('test upsertProject', () async {
      // TODO
    });

    //Future<bool> upsertRole(TbRole tbRole) async
    test('test upsertRole', () async {
      // TODO
    });

  });
}
