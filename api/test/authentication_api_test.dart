import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for AuthenticationApi
void main() {
  final instance = Openapi().getAuthenticationApi();

  group(AuthenticationApi, () {
    //Future<String> anonymousLogin() async
    test('test anonymousLogin', () async {
      // TODO
    });

    //Future<String> ping() async
    test('test ping', () async {
      // TODO
    });

    // 验证令牌
    //
    // 验证JWT令牌是否有效并续期
    //
    //Future<String> validateToken(String authorization) async
    test('test validateToken', () async {
      // TODO
    });

  });
}
