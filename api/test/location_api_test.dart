import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for LocationApi
void main() {
  final instance = Openapi().getLocationApi();

  group(LocationApi, () {
    //Future<BuiltList<Location>> findLocationByAddress(String body, { String city }) async
    test('test findLocationByAddress', () async {
      // TODO
    });

    //Future<TbRegion> getLocationById(String id) async
    test('test getLocationById', () async {
      // TODO
    });

    //Future<BuiltList<String>> getLocationByIp() async
    test('test getLocationByIp', () async {
      // TODO
    });

    //Future<BuiltList<TbRegion>> getRegionsByParentId(String parentId) async
    test('test getRegionsByParentId', () async {
      // TODO
    });

  });
}
