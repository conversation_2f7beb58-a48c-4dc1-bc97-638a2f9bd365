import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for BudgetPlanningItem
void main() {
  final instance = BudgetPlanningItemBuilder();
  // TODO add properties to the builder and call build()

  group(BudgetPlanningItem, () {
    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String type
    test('to test the property `type`', () async {
      // TODO
    });

    // double budget
    test('to test the property `budget`', () async {
      // TODO
    });

    // int count
    test('to test the property `count`', () async {
      // TODO
    });

    // BuiltList<BudgetPlanningItem> children
    test('to test the property `children`', () async {
      // TODO
    });

  });
}
