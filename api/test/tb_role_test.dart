import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbRole
void main() {
  final instance = TbRoleBuilder();
  // TODO add properties to the builder and call build()

  group(TbRole, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // int index
    test('to test the property `index`', () async {
      // TODO
    });

    // String projectId
    test('to test the property `projectId`', () async {
      // TODO
    });

  });
}
