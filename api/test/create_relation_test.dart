import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for CreateRelation
void main() {
  final instance = CreateRelationBuilder();
  // TODO add properties to the builder and call build()

  group(CreateRelation, () {
    // String projectId
    test('to test the property `projectId`', () async {
      // TODO
    });

    // BuiltList<String> roleIds
    test('to test the property `roleIds`', () async {
      // TODO
    });

    // String displayName
    test('to test the property `displayName`', () async {
      // TODO
    });

    // String phone
    test('to test the property `phone`', () async {
      // TODO
    });

  });
}
