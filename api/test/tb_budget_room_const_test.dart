import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbBudgetRoomConst
void main() {
  final instance = TbBudgetRoomConstBuilder();
  // TODO add properties to the builder and call build()

  group(TbBudgetRoomConst, () {
    // String budgetTypeId
    test('to test the property `budgetTypeId`', () async {
      // TODO
    });

    // String roomId
    test('to test the property `roomId`', () async {
      // TODO
    });

    // int roomLaborFactor
    test('to test the property `roomLaborFactor`', () async {
      // TODO
    });

    // int roomMaterialFactor
    test('to test the property `roomMaterialFactor`', () async {
      // TODO
    });

  });
}
