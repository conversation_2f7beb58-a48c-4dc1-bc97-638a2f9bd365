import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for BudgetPlanningApi
void main() {
  final instance = Openapi().getBudgetPlanningApi();

  group(BudgetPlanningApi, () {
    //Future<BudgetPlanningItem> budgetPlanningItem() async
    test('test budgetPlanningItem', () async {
      // TODO
    });

    //Future<BuiltList<TbBudgetConst>> getBudgetConst() async
    test('test getBudgetConst', () async {
      // TODO
    });

    //Future<BuiltList<TbBudgetFactor>> getBudgetFactors() async
    test('test getBudgetFactors', () async {
      // TODO
    });

    //Future<BuiltList<TbBudgetRoomConst>> getRoomConst() async
    test('test getRoomConst', () async {
      // TODO
    });

    //Future<BuiltList<String>> plan(BudgetPlanningParameter budgetPlanningParameter) async
    test('test plan', () async {
      // TODO
    });

    //Future<bool> setBudgetConstFixedDeviceFactor(String typeId, { int fixedDeviceFactor }) async
    test('test setBudgetConstFixedDeviceFactor', () async {
      // TODO
    });

    //Future<bool> setBudgetConstLabor(String typeId, { int laborFactor }) async
    test('test setBudgetConstLabor', () async {
      // TODO
    });

    //Future<bool> setBudgetConstMaterial(String typeId, { int materialFactor }) async
    test('test setBudgetConstMaterial', () async {
      // TODO
    });

    //Future<bool> setBudgetFactor(String budgetTypeId, String key, String attributeId, { int factor }) async
    test('test setBudgetFactor', () async {
      // TODO
    });

    //Future<bool> setRoomConstLabor(String budgetTypeId, String roomId, { int laborFactor }) async
    test('test setRoomConstLabor', () async {
      // TODO
    });

    //Future<bool> setRoomConstMaterial(String budgetTypeId, String roomId, { int materialFactor }) async
    test('test setRoomConstMaterial', () async {
      // TODO
    });

  });
}
