import 'package:test/test.dart';
import 'package:openapi/openapi.dart';


/// tests for TaskApi
void main() {
  final instance = Openapi().getTaskApi();

  group(TaskApi, () {
    //Future<BuiltList<TbTask>> getAssignedTasks() async
    test('test getAssignedTasks', () async {
      // TODO
    });

    //Future<BuiltList<TbTask>> getReceivedTasks() async
    test('test getReceivedTasks', () async {
      // TODO
    });

    //Future<BuiltList<TbTask>> getTasksInPlan(String planId) async
    test('test getTasksInPlan', () async {
      // TODO
    });

    //Future<bool> upsertTask(CreateTaskRequest createTaskRequest) async
    test('test upsertTask', () async {
      // TODO
    });

  });
}
