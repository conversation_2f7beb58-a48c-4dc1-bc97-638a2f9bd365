import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbBudgetItem
void main() {
  final instance = TbBudgetItemBuilder();
  // TODO add properties to the builder and call build()

  group(TbBudgetItem, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String parentId
    test('to test the property `parentId`', () async {
      // TODO
    });

    // String budgetId
    test('to test the property `budgetId`', () async {
      // TODO
    });

    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // int index
    test('to test the property `index`', () async {
      // TODO
    });

    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // double quantity
    test('to test the property `quantity`', () async {
      // TODO
    });

    // double unitPrice
    test('to test the property `unitPrice`', () async {
      // TODO
    });

    // double lineAmount
    test('to test the property `lineAmount`', () async {
      // TODO
    });

    // bool checked
    test('to test the property `checked`', () async {
      // TODO
    });

    // String url
    test('to test the property `url`', () async {
      // TODO
    });

    // int sizeHeight
    test('to test the property `sizeHeight`', () async {
      // TODO
    });

    // int sizeWidth
    test('to test the property `sizeWidth`', () async {
      // TODO
    });

    // int sizeDepth
    test('to test the property `sizeDepth`', () async {
      // TODO
    });

    // String contactUserId
    test('to test the property `contactUserId`', () async {
      // TODO
    });

    // String contactName
    test('to test the property `contactName`', () async {
      // TODO
    });

    // String contactPhone
    test('to test the property `contactPhone`', () async {
      // TODO
    });

    // String model
    test('to test the property `model`', () async {
      // TODO
    });

    // String platform
    test('to test the property `platform`', () async {
      // TODO
    });

    // String type
    test('to test the property `type`', () async {
      // TODO
    });

    // BuiltList<String> paymentRecords
    test('to test the property `paymentRecords`', () async {
      // TODO
    });

  });
}
