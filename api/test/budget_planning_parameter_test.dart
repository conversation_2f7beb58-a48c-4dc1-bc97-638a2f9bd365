import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for BudgetPlanningParameter
void main() {
  final instance = BudgetPlanningParameterBuilder();
  // TODO add properties to the builder and call build()

  group(BudgetPlanningParameter, () {
    // String budgetId
    test('to test the property `budgetId`', () async {
      // TODO
    });

    // double area
    test('to test the property `area`', () async {
      // TODO
    });

    // String region
    test('to test the property `region`', () async {
      // TODO
    });

    // double budgetAmount
    test('to test the property `budgetAmount`', () async {
      // TODO
    });

    // BuiltMap<String, String> attributeTypeValues
    test('to test the property `attributeTypeValues`', () async {
      // TODO
    });

    // BuiltMap<String, int> rooms
    test('to test the property `rooms`', () async {
      // TODO
    });

  });
}
