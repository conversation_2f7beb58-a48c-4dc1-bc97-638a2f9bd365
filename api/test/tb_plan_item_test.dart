import 'package:test/test.dart';
import 'package:openapi/openapi.dart';

// tests for TbPlanItem
void main() {
  final instance = TbPlanItemBuilder();
  // TODO add properties to the builder and call build()

  group(TbPlanItem, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String parentId
    test('to test the property `parentId`', () async {
      // TODO
    });

    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // int offsetDays
    test('to test the property `offsetDays`', () async {
      // TODO
    });

    // int durationDays
    test('to test the property `durationDays`', () async {
      // TODO
    });

    // bool isCompleted
    test('to test the property `isCompleted`', () async {
      // TODO
    });

  });
}
