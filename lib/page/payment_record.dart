import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:wheel_picker/wheel_picker.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/model/budget/budget_payment_value.dart';
import 'package:zxb/page/budget.dart';

class PaymentRecord extends StatefulWidget {
  final BudgetPaymentValue? paymentRecord;
  final double? remain;

  const PaymentRecord({super.key, this.paymentRecord, this.remain});

  @override
  State<StatefulWidget> createState() => _PaymentRecordState();
}

class _PaymentRecordState extends State<PaymentRecord> {
  final _dateFocus = FocusNode();
  final _amountFocus = FocusNode();
  late TextEditingController _dateController;
  late TextEditingController _amountController;
  late WheelPickerController _yearController;
  late WheelPickerController _monthController;
  late WheelPickerController _dayController;
  final _dayUpdate = RxInt(0);
  late int year;
  late int month;
  late int day;

  @override
  void initState() {
    super.initState();
    _dateFocus.addListener(() {
      if (_dateFocus.hasFocus) {
        _dateFocus.unfocus();
        showTimePicker();
      }
    });
    _amountFocus.addListener(() {
      _amountController.selection = TextSelection(baseOffset: 0, extentOffset: _amountController.text.length);
    });

    _amountController = TextEditingController(text: widget.paymentRecord?.amount.toStringAsFixed(2) ?? "0.0");
    _dateController = _dateController = TextEditingController(
      text: widget.paymentRecord == null
          ? "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          : "${widget.paymentRecord!.time.value.year}-${widget.paymentRecord!.time.value.month}-${widget.paymentRecord!.time.value.day}",
    );
    year = widget.paymentRecord == null ? DateTime.now().year : widget.paymentRecord!.time.value.year;
    month = widget.paymentRecord == null ? DateTime.now().month : widget.paymentRecord!.time.value.month;
    day = widget.paymentRecord == null ? DateTime.now().day : widget.paymentRecord!.time.value.day;
    _yearController = WheelPickerController(itemCount: 190, initialIndex: year - 1990);
    _monthController = WheelPickerController(itemCount: 12, initialIndex: month - 1);
    _dayController = WheelPickerController(itemCount: _getDaysOfYearMonth(DateTime.now().year, DateTime.now().month), initialIndex: day - 1);
  }

  var days31 = {1, 3, 5, 7, 8, 10, 12};
  var days30 = {4, 6, 9, 11};

  void _updateDayController() {
    _dayController.itemCount = _getDaysOfYearMonth(_yearController.selected + 1990, _monthController.selected + 1);
    _dayUpdate.value += 1;
  }

  int _getDaysOfYearMonth(int year, int month) {
    if (days31.contains(month)) {
      return 31;
    } else if (days30.contains(month)) {
      return 30;
    } else {
      return _yearController.selected % 4 == 0 ? 29 : 28;
    }
  }

  void _updateDateText() {
    final year = _yearController.selected + 1990;
    final month = _monthController.selected + 1;
    final day = _dayController.selected + 1;
    _dateController.text = "$year-$month-$day";
  }

  @override
  void dispose() {
    _dateFocus.dispose();
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    _amountController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(SCAFFOLD_PADDING_HOR),
      child: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.black, width: 2),
          color: Theme.of(context).colorScheme.surface,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("付款记录", style: Theme.of(context).textTheme.titleMedium),
            Form(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("金额"),
                  SizedBox(height: 8),
                  TextFormField(
                    autofocus: true,
                    decoration: InputDecoration(
                      prefixText: "¥",
                      floatingLabelBehavior: FloatingLabelBehavior.always,
                      suffixIcon: IntrinsicWidth(
                        child: Padding(
                          padding: EdgeInsets.all(4),
                          child: Container(
                            width: 60,
                            decoration: BoxDecoration(color: Colors.black, borderRadius: BorderRadius.circular(6)),
                            alignment: Alignment.center,
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                _amountController.text = widget.remain?.toStringAsFixed(2) ?? "0.0";
                              },
                              child: Text("结清", style: TextStyle(color: Colors.white)),
                            ),
                          ),
                        ),
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    controller: _amountController,
                    focusNode: _amountFocus,
                    textAlign: TextAlign.right,
                  ),
                  SizedBox(height: 20),
                  Text("付款日期"),
                  SizedBox(height: 8),
                  TextFormField(focusNode: _dateFocus, controller: _dateController),
                  SizedBox(height: 30),
                  Row(
                    children: [
                      Visibility(
                        visible: widget.paymentRecord != null,
                        child: Expanded(
                          flex: 1,
                          child: FilledButton(
                            onPressed: () {
                              Get.back(result: true);
                            },
                            style: ButtonStyle(backgroundColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.error)),
                            child: Text("删除"),
                          ),
                        ),
                      ),
                      widget.paymentRecord != null ? SizedBox(width: 20) : SizedBox.shrink(),
                      Expanded(
                        flex: 2,
                        child: FilledButton(
                          onPressed: () {
                            Get.back(
                              result: (widget.paymentRecord ?? BudgetPaymentValue(null))
                                ..amount.value = double.tryParse(_amountController.text) ?? 0
                                ..time.value = DateTime(year, month, day),
                            );
                          },
                          child: Text("保存"),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showTimePicker() {
    showMaterialModalBottomSheet(
      context: context,
      builder: (ctx) => Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: 200,
            width: 80,
            child: WheelPicker(
              controller: _yearController,
              builder: (ctx, idx) {
                return Text("${idx + 1990}", style: Theme.of(context).textTheme.titleLarge);
              },
              onIndexChanged: (idx, type) {
                HapticFeedback.lightImpact();
                _updateDayController();
                _updateDateText();
                year = idx + 1990;
              },
              style: WheelPickerStyle(
                // itemExtent: textStyle.fontSize! * textStyle.height!, // Text height
                squeeze: 1.25,
                diameterRatio: .8,
                surroundingOpacity: .25,
                magnification: 1.2,
              ),
            ),
          ),
          SizedBox(
            height: 200,
            width: 80,
            child: WheelPicker(
              controller: _monthController,
              builder: (ctx, idx) {
                return Text("${idx + 1}", style: Theme.of(context).textTheme.titleLarge);
              },
              onIndexChanged: (idx, type) {
                HapticFeedback.lightImpact();
                _updateDayController();
                _updateDateText();
                month = idx + 1;
              },
              style: WheelPickerStyle(
                // itemExtent: textStyle.fontSize! * textStyle.height!, // Text height
                squeeze: 1.25,
                diameterRatio: .8,
                surroundingOpacity: .25,
                magnification: 1.2,
              ),
            ),
          ),
          SizedBox(
            height: 200,
            width: 80,
            child: Obx(() {
              var x = _dayUpdate.value;
              return WheelPicker(
                controller: _dayController,
                builder: (ctx, idx) {
                  return Text("${idx + 1}", style: Theme.of(context).textTheme.titleLarge);
                },
                onIndexChanged: (idx, type) {
                  HapticFeedback.lightImpact();
                  _updateDateText();
                  day = idx + 1;
                },
                style: WheelPickerStyle(
                  // itemExtent: textStyle.fontSize! * textStyle.height!, // Text height
                  squeeze: 1.25,
                  diameterRatio: .8,
                  surroundingOpacity: .25,
                  magnification: 1.2,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
