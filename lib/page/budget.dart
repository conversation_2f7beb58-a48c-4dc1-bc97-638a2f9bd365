import 'dart:math';

import 'package:built_collection/built_collection.dart';
import 'package:built_value/serializer.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/model/budget/budget_item_value.dart';
import 'package:zxb/model/budget/budget_type_value.dart';
import 'package:zxb/page/budget_toolbar.dart';
import 'package:zxb/page/create_budget_item.dart';
import 'package:zxb/page/house_attributes_editor.dart';
import 'package:zxb/page/share_budget.dart';
import 'package:zxb/page/simple_create_budget_item.dart';
import 'package:zxb/until/any_ext.dart';
import 'package:zxb/until/finance.dart';
import 'package:zxb/until/id.dart';
import 'package:zxb/until/sse.dart';

class Budget extends StatefulWidget {
  final String budgetId;

  const Budget({super.key, required this.budgetId});

  @override
  State<StatefulWidget> createState() => _BudgetState();
}

class _BudgetState extends State<Budget> {
  late List<TbBudgetType> _types;
  late Future<void> _initFunction;

  final _visibleTypes = RxList<BudgetTypeValue>();
  final _expandedTypes = <String, RxBool>{};

  final _totalAmount = RxDouble(0);
  final _spentAmount = RxDouble(0);
  final _remainAmount = RxDouble(0);

  final _workers = <Worker>[];
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  final _listController = ScrollController();

  final _TOTAL_HEIGHT = 122.0;
  final BUDGET_ITEM_HEIGHT = 40.0;
  final MAIN_TYPE_HEADER_HEIGHT = 80.0;
  final GlobalKey<BudgetToolbarState> _toolbarKey = GlobalKey<BudgetToolbarState>();

  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _initFunction = _init();
    _visibleTypes.listen((_) {
      for (var v in _workers) {
        v.dispose();
      }
      for (var item in _visibleTypes) {
        _workers.add(ever(item.totalAmount, (_) => _summary()));
        _workers.add(ever(item.spent, (_) => _summary()));
      }
    });
    _summary();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _overlayEntry?.remove();
      _overlayEntry = OverlayEntry(
        builder: (ctx) {
          return BudgetToolbar(
            key: _toolbarKey,
            onAddButtonClick: () {
              createOrEditItem();
            },
            onAiButtonClick: () {},
            onShareButtonClick: () {},
            onDeleteButtonClick: () {},
            onLinkButtonClick: () {},
          );
        },
      );
      Overlay.of(context).insert(_overlayEntry!);
    });
  }

  void _summary() {
    _totalAmount.value = _visibleTypes.isEmpty ? 0 : _visibleTypes.map((v) => v.totalAmount.value).reduce((a, b) => a + b);
    _spentAmount.value = _visibleTypes.isEmpty ? 0 : _visibleTypes.map((v) => v.spent.value).reduce((a, b) => a + b);
    _remainAmount.value = _totalAmount.value - _spentAmount.value;
  }

  @override
  void dispose() {
    _overlayEntry?.remove();
    _listController.dispose();
    super.dispose();
  }

  Future<void> createOrEditItem({BudgetItemValue? value, String? mainTypeId}) async {
    await showMaterialModalBottomSheet(context: context, builder: (ctx){
      return SimpleCreateBudgetItem();
    });
    final result = await showMaterialModalBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      builder: (ctx) {
        return CreateBudgetItem(budgetId: widget.budgetId, budgetTypes: _types, item: value?.copy(), initBudgetMainType: mainTypeId);
      },
      expand: true,
    );
    if (result == true && value != null) {
      var type = _visibleTypes.firstWhereOrNull((v) => v.id == _types.firstWhereOrNull((w) => w.id == value.type.value)?.parent);
      if (type != null) {
        type.removeChild(value);
        if (type.children.isEmpty) {
          _visibleTypes.remove(type);
        }
      }
    } else if (result != null && result is BudgetItemValue) {
      if (value == null) {
        // create
        var type = _visibleTypes.firstWhereOrNull((v) => v.id == _types.firstWhereOrNull((w) => w.id == result.type.value)?.parent);
        if (type == null) {
          type = BudgetTypeValue(_types.firstWhereOrNull((v) => v.id == _types.firstWhereOrNull((w) => w.id == result.type.value)!.parent)!);
          _visibleTypes.add(type);
          _visibleTypes.sortBy((a) => a.index);
        }
        type.appendChild(result);
      } else {
        value.mergeFrom(result);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _initFunction,
      builder: (ctx, snp) {
        if (snp.hasError) {
          return Container(
            height: 300,
            alignment: Alignment.center,
            child: Center(child: Text("出错了...")),
          );
        }
        if (snp.connectionState != ConnectionState.done) {
          return Container(height: 300, alignment: Alignment.center, child: CircularProgressIndicator());
        }
        return SafeArea(
          bottom: false,
          child: Obx(
            () => IndexedStack(
              index: _visibleTypes.isEmpty ? 0 : 1,
              children: [
                empty(),
                Container(
                  padding: EdgeInsets.only(left: SCAFFOLD_PADDING_HOR, right: SCAFFOLD_PADDING_HOR, top: 12),
                  child: Column(
                    children: [
                      totalHeader(),
                      SizedBox(height: 12),
                      Expanded(
                        child: AnimatedList.separated(
                          padding: EdgeInsets.only(bottom: 80),
                          key: _listKey,
                          controller: _listController,
                          itemBuilder: (ctx, idx, ani) {
                            return FadeTransition(opacity: ani, child: mainTypeContainer(_visibleTypes[idx]));
                          },
                          initialItemCount: _visibleTypes.length,
                          separatorBuilder: (BuildContext context, int index, ani) {
                            return SizedBox(height: 12);
                          },
                          removedSeparatorBuilder: (BuildContext context, int index, ani) {
                            return SizedBox(height: 12);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget empty() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FilledButton(
            onPressed: () async {
              _toolbarKey.currentState?.setToolbarStatus(-1);
              final result =
                  (await showCupertinoModalBottomSheet(
                        context: context,
                        barrierColor: Colors.black54,
                        builder: (ctx) {
                          return HouseAttributesEditor();
                        },
                      ))
                      as BudgetPlanningParameter?;
              if (result != null) {
                aiPlan((result.toBuilder()..budgetId = widget.budgetId).build());
              } else {
                _toolbarKey.currentState?.setToolbarStatus(2);
              }
            },
            style: ButtonStyle(backgroundColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.primary)),
            child: Text("AI做预算"),
          ),
          SizedBox(height: 20),
          Text("或者"),
          SizedBox(height: 20),
          Text("点击下方按钮添加预算"),
        ],
      ),
    );
  }

  Future<void> _init() async {
    _types = (await api.getBudgetTypeApi().getBudgetTypes()).data!.toList();
    final items = (await api.getBudgetApi().getBudgetItems(budgetId: widget.budgetId)).data?.toList() ?? [];
    for (var item in items) {
      final itemValue = BudgetItemValue(item);
      final subType = _types.firstWhere((t) => t.id == item.type);
      final mainType = _types.firstWhere((t) => t.id == subType.parent);
      var visible = _visibleTypes.firstWhereOrNull((t) => t.id == mainType.id);
      if (visible == null) {
        visible = BudgetTypeValue(mainType);
        _visibleTypes.add(visible);
      }
      visible.children.add(itemValue);
    }
    for (var item in _visibleTypes) {
      item.children.sort((a, b) => a.index.value.compareTo(b.index.value));
      item.init();
    }
    _visibleTypes.sort((a, b) => a.index.compareTo(b.index));
  }

  totalHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      width: double.maxFinite,
      height: _TOTAL_HEIGHT,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: Theme.of(context).colorScheme.primaryContainer),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("总预算", style: Theme.of(context).textTheme.bodySmall),
              Obx(() => Text(_totalAmount.value.toUIString(), style: Theme.of(context).textTheme.displaySmall)),
              SizedBox(height: 4),
              Row(
                children: [
                  Container(height: 36, width: 2, color: Theme.of(context).colorScheme.primary),
                  SizedBox(width: 6),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("已支出", style: Theme.of(context).textTheme.bodySmall),
                      SizedBox(height: 2),
                      Obx(() => Text(_spentAmount.value.toUIString(), style: Theme.of(context).textTheme.bodyMedium)),
                    ],
                  ),
                  SizedBox(width: 20),
                  Container(height: 36, width: 2, color: Theme.of(context).colorScheme.secondary),
                  SizedBox(width: 6),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("待支出", style: Theme.of(context).textTheme.bodySmall),
                      SizedBox(height: 2),
                      Obx(() => Text(_remainAmount.value.toUIString(), style: Theme.of(context).textTheme.bodyMedium)),
                    ],
                  ),
                ],
              ),
            ],
          ),
          Spacer(),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              FilledButton(
                onPressed: () async {
                  shareBudget(context, _visibleTypes);
                },
                style: ButtonStyle(
                  minimumSize: WidgetStatePropertyAll(Size(0, 30)),
                  backgroundColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.secondary),
                ),
                child: Text("分享"),
              ),
            ],
          ),
        ],
      ),
    );
  }

  mainTypeContainer(BudgetTypeValue item) {
    if (!_expandedTypes.containsKey(item.id)) {
      _expandedTypes[item.id] = RxBool(true);
    }
    return Obx(
      () => AnimatedContainer(
        height: MAIN_TYPE_HEADER_HEIGHT + BUDGET_ITEM_HEIGHT * item.children.length * (_expandedTypes[item.id]!.value ? 1 : 0) + 2,
        duration: Duration(milliseconds: 200),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Theme.of(context).colorScheme.outlineVariant),
        ),
        child: ClipRRect(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: _expandedTypes[item.id]!.value
                ? ([mainTypeHeader(item)] + item.children.map((i) => budgetItem(i)).toList(growable: false))
                : [mainTypeHeader(item)],
          ),
        ),
      ),
    );
  }

  Widget budgetItem(BudgetItemValue item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        createOrEditItem(value: item);
      },
      child: Container(
        height: BUDGET_ITEM_HEIGHT,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 12),
        child: Row(children: [Obx(() => Text(item.title.value)), Spacer(), Obx(() => Text(item.totalAmount.value?.toStringAsFixed(2) ?? ""))]),
      ),
    );
  }

  Widget mainTypeHeader(BudgetTypeValue item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      child: Obx(
        () => Container(
          height: MAIN_TYPE_HEADER_HEIGHT,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
              bottomLeft: Radius.circular(_expandedTypes[item.id]!.value == true ? 0 : 8),
              bottomRight: Radius.circular(_expandedTypes[item.id]!.value == true ? 0 : 8),
            ),
            // color: Theme.of(context).colorScheme.tertiaryContainer,
            border: Border(bottom: BorderSide(color: Theme.of(context).colorScheme.outlineVariant)),
          ),
          child: Stack(
            children: [
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                    bottomLeft: Radius.circular(_expandedTypes[item.id]!.value == true ? 0 : 8),
                    bottomRight: Radius.circular(_expandedTypes[item.id]!.value == true ? 0 : 8),
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: LayoutBuilder(
                      builder: (ctx, cons) {
                        final double progressRatio;
                        if (item.totalAmount.value == 0.0) {
                          progressRatio = 0.0;
                        } else {
                          progressRatio = item.spent.value / item.totalAmount.value;
                        }

                        return Container(
                          height: cons.maxHeight,
                          width: max(cons.maxWidth * progressRatio, 0),
                          decoration: BoxDecoration(color: Theme.of(context).colorScheme.secondaryContainer),
                        );
                      },
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: ITEM_SIZE,
                      height: ITEM_SIZE,
                      alignment: Alignment.center,
                      child: Image.asset("assets/icon/${item.id}.png", width: ITEM_SIZE - 8, height: ITEM_SIZE - 8),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(item.name, style: Theme.of(context).textTheme.titleMedium),
                              Spacer(),
                              Obx(() => Text("${item.children.length}项", style: Theme.of(context).textTheme.bodySmall)),
                            ],
                          ),
                          SizedBox(height: 4),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Expanded(
                                flex: 1,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("预算", style: Theme.of(context).textTheme.bodySmall),
                                    Obx(() => Text(item.totalAmount.value.toUIString(), style: Theme.of(context).textTheme.bodySmall)),
                                  ],
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("已支出", style: Theme.of(context).textTheme.bodySmall),
                                    Text((item.spent.value).toUIString(), style: Theme.of(context).textTheme.bodySmall),
                                  ],
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("待支出", style: Theme.of(context).textTheme.bodySmall),
                                    Text((item.left.value).toUIString(), style: Theme.of(context).textTheme.bodySmall),
                                  ],
                                ),
                              ),
                              GestureDetector(
                                child: Icon(Icons.add_circle_outline_rounded),
                                onTap: () {
                                  createOrEditItem(mainTypeId: item.id);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      onTap: () {
        var status = _expandedTypes[item.id];
        if (status == null) {
          status = RxBool(false);
          _expandedTypes[item.id] = status;
        }
        status.value = !status.value;
      },
    );
  }

  void aiPlan(BudgetPlanningParameter result) {
    _toolbarKey.currentState?.setToolbarStatus(-1);
    sseRequest(
      path: "/budget-planning/plan",
      data: result.let((v) {
        return standardSerializers.serialize(v, specifiedType: FullType(BudgetPlanningParameter))!;
      }),
      types: [
        TbBudgetItem,
        FullType(BuiltList, [FullType(TbBudgetItem)]),
      ],
      callback: (dynamic data) {
        if (data is TbBudgetItem) {
          final item = data;
          final type = _types.firstWhere((v) => v.id == item.type);
          final parent = _types.firstWhereOrNull((v) => v.id == type.parent)!;

          BudgetTypeValue? container = _visibleTypes.firstWhereOrNull((v) => v.id == parent.id);
          if (container == null) {
            container = BudgetTypeValue(parent);
            // if (_visibleTypes.isNotEmpty) {
            //   var exp = _expandedTypes[_visibleTypes.last.id];
            //   if (exp == null) {
            //     _expandedTypes[_visibleTypes.last.id] = RxBool(false);
            //   }
            //   _expandedTypes[_visibleTypes.last.id]!.value = false;
            // }
            _visibleTypes.add(container);
            _visibleTypes.sortBy((item) => item.index);
            _listKey.currentState?.insertItem(_visibleTypes.length - 1, duration: Duration(milliseconds: 1000));
          }
          HapticFeedback.lightImpact();
          container.appendChild(BudgetItemValue(item));
          Future.delayed(Duration(milliseconds: 100), () {
            if (_listController.hasClients) {
              _listController.animateTo(_listController.position.maxScrollExtent, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
            }
          });
        } else if (data is BuiltList<TbBudgetItem>) {
          HapticFeedback.heavyImpact();
          Future.delayed(Duration(milliseconds: 200), () {
            _listController.animateTo(0, duration: Duration(milliseconds: 300), curve: Curves.easeIn);
          });
          Fluttertoast.showToast(msg: "预算制作完成", toastLength: Toast.LENGTH_LONG, gravity: ToastGravity.CENTER);
          _toolbarKey.currentState?.setToolbarStatus(0);
          Future.delayed(Duration(milliseconds: 2000), () {
            _toolbarKey.currentState?.setToolbarStatus(1);
            _expandedTypes.forEach((key, v) => v.value = false);
          });
        }
      },
    );
  }
}
