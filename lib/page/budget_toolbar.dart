import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zxb/consts.dart';

class BudgetToolbar extends StatefulWidget {
  final Function() onAddButtonClick;
  final Function() onAiButtonClick;
  final Function() onShareButtonClick;
  final Function() onDeleteButtonClick;
  final Function() onLinkButtonClick;

  const BudgetToolbar({
    super.key,
    required this.onAddButtonClick,
    required this.onAiButtonClick,
    required this.onShareButtonClick,
    required this.onDeleteButtonClick,
    required this.onLinkButtonClick,
  });

  @override
  State<StatefulWidget> createState() => BudgetToolbarState();
}

class BudgetToolbarState extends State<BudgetToolbar> {
  /// -1: disable
  ///
  /// 0: left, AI button
  ///
  /// 1: full
  ///
  /// 2: right, addButton
  final RxInt _status = RxInt(2);

  void setToolbarStatus(int status) {
    _status.value = status;
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  final _size = 60.0;
  final _padding = 4.0;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      type: MaterialType.transparency,

      child: SafeArea(
        child: Obx(
          () => _status.value == -1
              ? SizedBox.shrink()
              : AnimatedOpacity(
                  opacity: _status.value == -1 ? 0 : 1,
                  duration: Duration(milliseconds: 300),
                  child: Align(
                    alignment: _status.value != 2 ? Alignment.bottomLeft : Alignment.bottomRight,
                    child: Container(
                      padding: EdgeInsets.only(left: SCAFFOLD_PADDING_HOR, right: SCAFFOLD_PADDING_HOR, bottom: 70),
                      child: Obx(
                        () => ClipRRect(
                          borderRadius: BorderRadius.circular(99999),
                          child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                            child: AnimatedContainer(
                              duration: Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              width: _status.value == 1 ? MediaQuery.of(context).size.width - SCAFFOLD_PADDING_HOR * 2 : _size,
                              height: _size,
                              decoration: BoxDecoration(
                                // 使用半透明颜色以获得毛玻璃效果
                                color: Theme.of(context).colorScheme.tertiaryContainer.withOpacity(0.8),
                                borderRadius: BorderRadius.circular(99999),
                                // 可选：添加边框以增强毛玻璃效果
                                border: Border.all(color: Theme.of(context).colorScheme.onTertiaryContainer.withOpacity(0.3), width: 0),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  if (_status.value == 0 || _status.value == 1)
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () => widget.onAiButtonClick,
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(horizontal: _padding),
                                        child: Container(
                                          height: _size - _padding * 2,
                                          width: _size - _padding * 2,
                                          decoration: BoxDecoration(
                                            color: Theme.of(context).colorScheme.onTertiaryContainer,
                                            borderRadius: BorderRadius.circular(99999),
                                          ),
                                          alignment: Alignment.center,
                                          child: Text("AI", style: TextStyle(color: Theme.of(context).colorScheme.tertiaryContainer, fontSize: 24)),
                                        ),
                                      ),
                                    ),
                                  if (_status.value == 1)
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () => widget.onLinkButtonClick,
                                      child: SizedBox(
                                        width: _size - _padding * 2,
                                        height: _size - _padding * 2,
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(CupertinoIcons.link, color: Theme.of(context).colorScheme.onTertiaryContainer, size: 22),
                                            SizedBox(height: 4),
                                            Text(
                                              "关联项目",
                                              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onTertiaryContainer),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  if (_status.value == 1)
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () => widget.onShareButtonClick,
                                      child: SizedBox(
                                        width: _size - _padding * 2,
                                        height: _size - _padding * 2,
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.share, color: Theme.of(context).colorScheme.onTertiaryContainer, size: 22),
                                            SizedBox(height: 4),
                                            Text(
                                              "分享",
                                              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onTertiaryContainer),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  if (_status.value == 1)
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: widget.onDeleteButtonClick,
                                      child: SizedBox(
                                        width: _size - _padding * 2,
                                        height: _size - _padding * 2,
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.close_rounded, color: Theme.of(context).colorScheme.onTertiaryContainer, size: 22),
                                            SizedBox(height: 4),
                                            Text(
                                              "删除",
                                              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onTertiaryContainer),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  if (_status.value == 1 || _status.value == 2)
                                    Padding(
                                      padding: EdgeInsets.only(right: _padding, left: _padding),
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          widget.onAddButtonClick();
                                        },
                                        child: SizedBox(
                                          width: _size - _padding * 2,
                                          height: _size - _padding * 2,
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Icon(CupertinoIcons.add, color: Theme.of(context).colorScheme.onTertiaryContainer, size: 22),
                                              SizedBox(height: _status.value == 2 ? 0 : 4),
                                              Text(
                                                "添加",
                                                style: Theme.of(
                                                  context,
                                                ).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onTertiaryContainer),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}
