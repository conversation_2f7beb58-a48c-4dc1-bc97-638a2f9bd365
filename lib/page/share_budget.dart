import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_fancy_tree_view2/flutter_fancy_tree_view2.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:zxb/component/tree/drag_drop_tree_view.dart';
import 'package:zxb/component/tree/tree_item.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/model/budget/budget_item_value.dart';
import 'package:zxb/model/budget/budget_type_value.dart';

void shareBudget(BuildContext context, List<BudgetTypeValue> categories) {
  final root = TreeItem<dynamic>(id: "root", data: "root");
  for (var category in categories) {
    final cateItem = TreeItem<dynamic>(id: category.id, data: category);
    root.appendChild(cateItem);
    for (var item in category.children) {
      cateItem.appendChild(TreeItem(id: item.id, data: item));
    }
  }
  final treeController = TreeController(roots: root.children, childrenProvider: (c) => c.children, parentProvider: (c) => c.parent);
  final pageController = PageController(initialPage: 0);
  final pageIndex = RxInt(0);
  final showShareButtons = false.obs;
  pageController.addListener(() {
    pageIndex.value = pageController.page?.toInt() ?? 0;
  });
  showCupertinoModalBottomSheet(
    context: context,
    builder: (ctx) {
      return Material(
        child: Obx(
          () => Container(
            height: showShareButtons.value ? 180 : MediaQuery.of(context).size.height,
            padding: EdgeInsets.only(left: SCAFFOLD_PADDING_HOR, right: SCAFFOLD_PADDING_HOR, top: 20),
            alignment: Alignment.topCenter,
            child: showShareButtons.value
                ? Container(
                    alignment: Alignment.topLeft,
                    padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                    width: double.maxFinite,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () {
                            FLUWX.share(WeChatShareTextModel("HEHE", scene: WeChatScene.session));
                          },
                          child: Column(children: [Image.asset("assets/icon/wechat.png", width: 40), SizedBox(height: 8), Text("微信")]),
                        ),
                        GestureDetector(
                          onTap: () {},
                          child: Column(children: [Image.asset("assets/icon/douyin.png", width: 40), SizedBox(height: 8), Text("抖音")]),
                        ),
                        GestureDetector(
                          onTap: () {},
                          child: Column(children: [Image.asset("assets/icon/xiaohongshu.png", width: 40), SizedBox(height: 8), Text("小红书")]),
                        ),
                        GestureDetector(
                          onTap: () {
                            FLUWX.share(WeChatShareTextModel("HEHE", scene: WeChatScene.timeline));
                          },
                          child: Column(children: [Image.asset("assets/icon/wechat-timeline.png", width: 40), SizedBox(height: 8), Text("朋友圈")]),
                        ),
                        GestureDetector(
                          onTap: () {
                            FLUWX.share(WeChatShareTextModel("HEHE", scene: WeChatScene.favorite));
                          },
                          child: Column(children: [Image.asset("assets/icon/wechat-favorite.png", width: 40), SizedBox(height: 8), Text("微信收藏")]),
                        ),
                      ],
                    ),
                  )
                : SafeArea(
                    child: Column(
                      children: [
                        Text("分享内容", style: Theme.of(context).textTheme.titleLarge),
                        SizedBox(height: 20),
                        Expanded(
                          child: PageView(
                            controller: pageController,
                            children: [
                              DragAndDropTreeView(
                                itemBuilder: (idx, item) {
                                  return Container(
                                    padding: EdgeInsets.symmetric(vertical: 8),
                                    child: () {
                                      if (item.data is BudgetTypeValue) {
                                        return Text((item.data as BudgetTypeValue).name);
                                      } else if (item.data is BudgetItemValue) {
                                        return Text((item.data as BudgetItemValue).title.value);
                                      }
                                      return SizedBox.shrink();
                                    }(),
                                  );
                                },
                                controller: treeController,
                                root: root,
                              ),
                              ListView(
                                children: [
                                  ListTile(
                                    title: Text("名称"),
                                    leading: Checkbox(value: true, onChanged: (v) {}),
                                  ),
                                  ListTile(
                                    title: Text("价格"),
                                    leading: Checkbox(value: true, onChanged: (v) {}),
                                  ),
                                  ListTile(
                                    title: Text("链接信息"),
                                    leading: Checkbox(value: true, onChanged: (v) {}),
                                  ),
                                  ListTile(
                                    title: Text("联系人"),
                                    leading: Checkbox(value: true, onChanged: (v) {}),
                                  ),
                                  ListTile(
                                    title: Text("备注"),
                                    leading: Checkbox(value: true, onChanged: (v) {}),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [Text("分享时请注意保护个人隐私", style: Theme.of(context).textTheme.bodySmall)],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Expanded(
                              child: FilledButton(
                                onPressed: () {
                                  if (pageIndex.value == 0) {
                                    pageController.nextPage(duration: Duration(milliseconds: 300), curve: Curves.ease);
                                  } else {
                                    showShareButtons.value = true;
                                  }
                                },
                                child: Obx(() => Text(pageIndex.value == 0 ? "下一步" : "分享")),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
          ),
        ),
      );
    },
  );
}
