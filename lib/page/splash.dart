import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/page/home.dart';
import 'package:zxb/page/login.dart';
import 'package:zxb/security.dart';

class Splash extends StatefulWidget {
  const Splash({super.key});

  @override
  State<StatefulWidget> createState() => _SplashState();
}

class _SplashState extends State<Splash> {
  @override
  void initState() {
    super.initState();
    _checkSessionAndNavigate();
  }

  final _connected = RxnBool();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Obx(() {
          if (_connected.value == null || _connected.value == true) {
            return CircularProgressIndicator();
          } else {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("无法连接，请检查您的网络。"),
                <PERSON><PERSON><PERSON><PERSON>(height: 40),
                FilledButton(
                  onPressed: () {
                    _checkSessionAndNavigate();
                  },
                  child: Row(mainAxisSize: MainAxisSize.min, children: [Icon(Icons.refresh), Text("  重试")]),
                ),
              ],
            );
          }
        }),
      ),
    );
  }

  Future<void> _checkSessionAndNavigate() async {
    try {
      await api.getAuthenticationApi().ping();
      _connected.value = true;
    } catch (ex) {
      print('Network Error: $ex');
      _connected.value = false;
      return;
    }
    try {
      final isValid = await _checkSession();
      if (mounted) {
        if (isValid) {
          final response = await api.getUserControllerApi().getCurrentUser();
          CURRENT_USER.value = response.data;
          Get.offAll(() => const Home());
        } else {
          if (await getToken() == null) {
            final token = (await api.getAuthenticationApi().anonymousLogin()).data;
            if (token != null) {
              await setToken(token);
              final response = await api.getUserControllerApi().getCurrentUser();
              CURRENT_USER.value = response.data;
              Get.offAll(() => const Home());
            }
          } else {
            Get.offAll(() => const Login());
          }
        }
      }
    } catch (e) {
      if (mounted) {
        Get.offAll(() => const Login());
      }
    }
  }

  Future<bool> _checkSession() async {
    final token = await getToken();
    if (token == null) {
      return false;
    }
    final response = await api.getAuthenticationApi().validateToken(authorization: token);
    if (response.data != null) {
      setToken(response.data!);
      return true;
    } else {
      return false;
    }
  }
}
