import 'package:built_collection/built_collection.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/component/default_selector.dart';
import 'package:zxb/component/region_selector.dart';
import 'package:zxb/component/room_layout_selector.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/until/list_ext.dart';

class HouseAttributesEditor extends StatefulWidget {
  const HouseAttributesEditor({super.key});

  @override
  State<StatefulWidget> createState() => _HouseAttributesEditorState();
}

class _HouseAttributesEditorState extends State<HouseAttributesEditor> {
  final _selectedProvince = Rx<TbRegion>(
    (TbRegionBuilder()
          ..id = "110000"
          ..name = "北京市"
          ..parent = "100000"
          ..centerLat = 39.904179
          ..centerLon = 116.407387
          ..type = TbRegionTypeEnum.city)
        .build(),
  );
  final _selectedCity = Rx<TbRegion>(
    (TbRegionBuilder()
          ..id = "110101"
          ..name = "东城区"
          ..parent = "110000"
          ..centerLat = 39.928359
          ..centerLon = 116.416334
          ..type = TbRegionTypeEnum.city)
        .build(),
  );
  final _areaController = TextEditingController(text: "120");
  var _rooms = <TbRoomType>[];
  final _roomLayout = RxMap<String, int>();
  final _attributeTypes = RxList<TbHouseAttributeType>();
  final _attributes = RxList<TbHouseAttribute>();
  final _selectedRenovationStyle = Rxn<TbHouseAttribute>();
  final _renovationStyles = RxList<TbHouseAttribute>();
  final _selectedHouseType = Rxn<TbHouseAttribute>();
  final _houseTypes = RxList<TbHouseAttribute>();
  final _selectedRenovationLevel = Rxn<TbHouseAttribute>();
  final _renovationLevels = RxList<TbHouseAttribute>();
  final _pageController = PageController(viewportFraction: 0.7);

  final STYLE_PAGEVIEW_FRACTION = 0.7;
  final HOUSE_TYPE = "mokjsth7";
  final RENOVATION_LEVEL = "ldwkdy1y";
  final RENOVATION_STYLE = "ml6yangx";
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    api.getLocationApi().getLocationByIp().then((data) {
      if (data.data != null && data.data!.length == 2) {
        api.getLocationApi().getLocationById(id: data.data![0]).then((value) {
          if (value.data != null) {
            _selectedProvince.value = value.data!;
          }
        });
        api.getLocationApi().getLocationById(id: data.data![1]).then((value) {
          if (value.data != null) {
            _selectedCity.value = value.data!;
          }
        });
      }
    });
    api.getBasicApi().getHouseAttributeTypes().then((data) {
      _attributeTypes.value = (data.data?.toList() ?? []).where((item) => item.defaultVisiable == true).toList().sortedBy((item) => item.index!);
    });
    api
        .getBasicApi()
        .getHouseAttributes()
        .then((data) {
          _attributes.value = (data.data?.toList() ?? []).toList().sortedBy((item) => item.index!);
          _selectedHouseType.value = _attributes.firstWhereOrNull((item) => item.type == HOUSE_TYPE && item.isDefault == true);
          _houseTypes.value = _attributes.where((item) => item.type == HOUSE_TYPE).toList().sortedBy((item) => item.index!);
          _selectedRenovationLevel.value = _attributes.firstWhereOrNull((item) => item.type == RENOVATION_LEVEL && item.isDefault == true);
          _renovationLevels.value = _attributes.where((item) => item.type == RENOVATION_LEVEL).toList().sortedBy((item) => item.index!);
          _selectedRenovationStyle.value = _attributes.firstWhereOrNull((item) => item.type == RENOVATION_STYLE && item.isDefault == true);
          _renovationStyles.value = _attributes.where((item) => item.type == RENOVATION_STYLE).toList().sortedBy((item) => item.index!);
          _pageController.animateToPage(
            _renovationStyles.indexWhere((item) => item.isDefault == true),
            duration: Duration(milliseconds: 100),
            curve: Curves.bounceInOut,
          );
        })
        .onError((err, a) {
          print(err);
        });
    api.getBasicApi().getRoomTypes().then((data) {
      _rooms = data.data?.toList().sortedBy((item) => item.index!) ?? [];
      _roomLayout.value = Map.fromEntries(data.data?.map((item) => MapEntry(item.id!, item.defaultCount ?? 0)) ?? []);
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final pageViewHeight = (MediaQuery.of(context).size.width - 40.0) * STYLE_PAGEVIEW_FRACTION * 9.0 / 16.0;
    return Material(
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Form(
                key: _formKey,
                child: TextFormField(
                  controller: _areaController,
                  decoration: InputDecoration(hintText: "请输入室内面积", suffixText: "m²", floatingLabelBehavior: FloatingLabelBehavior.always),
                  validator: (value) {
                    if (value == null || double.tryParse(value) == null) {
                      return "请输入室内面积";
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(height: 12),
              GestureDetector(
                onTap: () {
                  showCupertinoModalBottomSheet(
                    context: context,
                    builder: (ctx) {
                      return RoomLayoutSelector(
                        roomLayout: {},
                        onChanged: (String roomId, int roomCount) {
                          _roomLayout[roomId] = roomCount;
                        },
                      );
                    },
                  );
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: BoxBorder.all(color: Colors.grey.shade500, width: 1),
                  ),
                  child: Obx(() {
                    _roomLayout.value;
                    return Row(
                      children: _rooms
                          .where((item) => _roomLayout.containsKey(item.id) && _roomLayout[item.id]! > 0)
                          .map((item) {
                            return Text("${_roomLayout[item.id]}${item.shortName}", style: Theme.of(context).textTheme.bodyLarge);
                          })
                          .cast<Widget>()
                          .toList()
                          .joinItem(
                            (idx) => Container(
                              padding: EdgeInsets.symmetric(horizontal: 6),
                              child: Container(height: 20, width: 1, color: Colors.grey),
                            ),
                          )
                          .toList(),
                    );
                  }),
                ),
              ),
              SizedBox(height: 12),
              GestureDetector(
                onTap: () {
                  showCupertinoModalBottomSheet(
                    context: context,
                    builder: (ctx) {
                      return RegionSelector(
                        onSelectedProvince: (region) {
                          _selectedProvince.value = region;
                        },
                        onSelectedCity: (region) {
                          _selectedCity.value = region;
                        },
                        province: _selectedProvince.value.id,
                        city: _selectedCity.value.id,
                      );
                    },
                  );
                },
                child: Row(
                  children: [
                    Expanded(
                      child: Obx(
                        () => Container(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: BoxBorder.all(color: Colors.grey.shade500, width: 1),
                          ),
                          child: Row(
                            children: [
                              Text(_selectedProvince.value.name, style: Theme.of(context).textTheme.bodyLarge),
                              Spacer(),
                              Transform.scale(scale: 1.3, child: Icon(Icons.arrow_drop_down)),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Obx(
                        () => Container(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: BoxBorder.all(color: Colors.grey.shade500, width: 1),
                          ),
                          child: Row(
                            children: [
                              Text(_selectedCity.value.name, style: Theme.of(context).textTheme.bodyLarge),
                              Spacer(),
                              Transform.scale(scale: 1.3, child: Icon(Icons.arrow_drop_down)),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        showCupertinoModalBottomSheet(
                          context: context,
                          builder: (ctx) {
                            return DefaultSelector(
                              items: _houseTypes.map((item) => item.name).toList(),
                              onChanged: (idx) {
                                _selectedHouseType.value = _houseTypes[idx];
                              },
                              initSelectedIndex: _houseTypes.indexOf(_selectedHouseType.value),
                            );
                          },
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: BoxBorder.all(color: Colors.grey.shade500, width: 1),
                        ),
                        child: Row(
                          children: [
                            Obx(() => Text(_selectedHouseType.value?.name ?? "房屋类型", style: Theme.of(context).textTheme.bodyLarge)),
                            Spacer(),
                            Transform.scale(scale: 1.3, child: Icon(Icons.arrow_drop_down)),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        showCupertinoModalBottomSheet(
                          context: context,
                          builder: (ctx) {
                            return DefaultSelector(
                              items: _renovationLevels.map((item) => item.name).toList(),
                              onChanged: (idx) {
                                _selectedRenovationLevel.value = _renovationLevels[idx];
                              },
                              initSelectedIndex: _renovationLevels.indexOf(_selectedRenovationLevel.value),
                            );
                          },
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: BoxBorder.all(color: Colors.grey.shade500, width: 1),
                        ),
                        child: Row(
                          children: [
                            Obx(() => Text(_selectedRenovationLevel.value?.name ?? "装修档次", style: Theme.of(context).textTheme.bodyLarge)),
                            Spacer(),
                            Transform.scale(scale: 1.3, child: Icon(Icons.arrow_drop_down)),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              SizedBox(
                height: pageViewHeight,
                child: Obx(
                  () => PageView.builder(
                    onPageChanged: (idx) {
                      _selectedRenovationStyle.value = _renovationStyles[idx];
                    },
                    controller: _pageController,
                    itemBuilder: (ctx, idx) {
                      final item = _renovationStyles[idx];
                      return Container(
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        child: AspectRatio(
                          aspectRatio: 16 / 9, // 设置16:9比例
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Stack(
                              children: [
                                Positioned.fill(child: Image.network("https://huanxinjia.oss-cn-shanghai.aliyuncs.com/assets/6vrevgey.jpg", fit: BoxFit.cover)),
                                Positioned(
                                  left: 0,
                                  right: 0,
                                  bottom: 0,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                    color: Colors.black38,
                                    child: Text(
                                      item.name,
                                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    itemCount: _renovationStyles.length,
                  ),
                ),
              ),
              SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: FilledButton(
                      onPressed: () {
                        if (_formKey.currentState?.validate() != true) {
                          return;
                        }
                        final area = double.tryParse(_areaController.text);
                        final rooms = _roomLayout;
                        final province = _selectedProvince.value;
                        final city = _selectedCity.value;
                        final houseType = _selectedHouseType.value;
                        final renovationLevel = _selectedRenovationLevel.value;
                        final renovationStyle = _selectedRenovationStyle.value;

                        Get.back(
                          result:
                              (BudgetPlanningParameterBuilder()
                                    ..budgetId = ""
                                    ..area = area
                                    ..rooms = MapBuilder(rooms)
                                    ..region = city.id
                                    ..attributeTypeValues = MapBuilder({
                                      HOUSE_TYPE: houseType!.id,
                                      RENOVATION_LEVEL: renovationLevel!.id,
                                      RENOVATION_STYLE: renovationStyle!.id,
                                    }))
                                  .build(),
                        );
                      },
                      child: Text("生成报价"),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
