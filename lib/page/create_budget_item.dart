import 'package:built_collection/built_collection.dart';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:openapi/openapi.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:zxb/component/attachment.dart';
import 'package:zxb/component/budget_type_selector.dart';
import 'package:zxb/component/calculator.dart';
import 'package:zxb/component/default_alert_dialog.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/model/budget/budget_item_value.dart';
import 'package:zxb/model/budget/budget_payment_value.dart';
import 'package:zxb/page/budget.dart';
import 'package:zxb/page/payment_record.dart';
import 'package:zxb/until/id.dart';

class CreateBudgetItem extends StatefulWidget {
  final String budgetId;
  final List<TbBudgetType> budgetTypes;
  final BudgetItemValue? item;
  final String? initBudgetMainType;

  const CreateBudgetItem({super.key, required this.budgetId, required this.budgetTypes, required this.item, this.initBudgetMainType});

  @override
  State<StatefulWidget> createState() => _CreateBudgetItemState();
}

class _CreateBudgetItemState extends State<CreateBudgetItem> {
  late TextEditingController _titleController;
  late TextEditingController _totalAmountController;
  final _totalAmountFocus = FocusNode();

  final _mainTypeFocusNode = FocusNode();
  final _subTypeFocusNode = FocusNode();
  late TextEditingController _mainTypeController;
  late TextEditingController _subTypeController;

  late TextEditingController _contactNameController;
  late TextEditingController _contactPhoneController;

  late TextEditingController _urlController;

  late TextEditingController _sizeWidthController;
  late TextEditingController _sizeDepthController;
  late TextEditingController _sizeHeightController;
  late TextEditingController _descriptionController;

  late Calculator _calculator;
  final _sizeUnit = Rx<String>("mm");
  final _selectedBudgetMainType = RxnString();
  final _selectedBudgetType = RxnString();

  final _formKey = GlobalKey<FormState>();

  final _paymentRecords = RxList<BudgetPaymentValue>();

  // 用于记录records是自动生成还是手动输入的，如果是没有手动编辑过，在输入总金额的时候会自动带到records中
  var _manual = false;

  // 记录内容是否有变更且未更新
  var _needUpdate = false;

  final _pasteLoading = false.obs;

  @override
  void initState() {
    super.initState();
    _manual = widget.item?.paymentRecords.isNotEmpty == true;
    _titleController = TextEditingController(text: widget.item?.title.value);
    _totalAmountController = TextEditingController(text: widget.item?.totalAmount.value?.toString() ?? "");
    _totalAmountFocus.addListener(() {
      if (_totalAmountFocus.hasFocus) {
        _calculator.showKeyboardToolbar(_totalAmountController);
      } else {
        _calculator.hideKeyboardToolbar();
      }
      _totalAmountController.selection = TextSelection(baseOffset: 0, extentOffset: _totalAmountController.text.length);
    });
    _totalAmountController.addListener(() {
      if (!_manual) {
        // 这里暂时注释掉，之前可能是考虑当前内容是通过其他方式识别得到的？
        // if (_titleController.text.isNotEmpty && _paymentRecords.isEmpty) {
        if (_paymentRecords.isEmpty) {
          _paymentRecords.add(
            BudgetPaymentValue(null)
              ..time.value = DateTime.now()
              ..amount.value = 0,
          );
        }
        _paymentRecords.firstOrNull?.amount.value = double.tryParse(_totalAmountController.text) ?? 0;
      }
    });

    _mainTypeFocusNode.addListener(_typeListen);
    _subTypeFocusNode.addListener(_typeListen);
    _selectedBudgetType.value = widget.item?.type.value;
    _mainTypeController = TextEditingController(
      text: widget.budgetTypes.firstWhereOrNull((v) => v.id == widget.budgetTypes.firstWhereOrNull((w) => w.id == widget.item?.type.value)?.parent)?.name,
    );
    _subTypeController = TextEditingController(text: widget.budgetTypes.firstWhereOrNull((w) => w.id == widget.item?.type.value)?.name);

    if (widget.item == null && widget.initBudgetMainType != null) {
      _mainTypeController.text = widget.budgetTypes.firstWhereOrNull((v) => v.id == widget.initBudgetMainType)?.name ?? "";
      _selectedBudgetMainType.value = widget.initBudgetMainType;
    }

    _contactNameController = TextEditingController(text: widget.item?.contactName.value);

    _contactPhoneController = TextEditingController(text: widget.item?.contactPhone.value);

    _urlController = TextEditingController(text: widget.item?.url.value);

    _sizeWidthController = TextEditingController(text: widget.item?.sizeWidth.value?.toString());
    _sizeDepthController = TextEditingController(text: widget.item?.sizeDepth.value?.toString());
    _sizeHeightController = TextEditingController(text: widget.item?.sizeHeight.value?.toString());

    _descriptionController = TextEditingController(text: widget.item?.description.value);

    _paymentRecords.value = widget.item?.paymentRecords.toList() ?? [];

    _calculator = Calculator(context);
  }

  @override
  void dispose() {
    _subTypeFocusNode.removeListener(_typeListen);
    _mainTypeFocusNode.removeListener(_typeListen);
    _subTypeFocusNode.dispose();
    _mainTypeFocusNode.dispose();
    _mainTypeController.dispose();
    _subTypeController.dispose();
    _totalAmountFocus.dispose();
    _totalAmountController.dispose();
    super.dispose();
  }

  void _typeListen() {
    if (_mainTypeFocusNode.hasFocus || _subTypeFocusNode.hasFocus) {
      _mainTypeFocusNode.unfocus();
      _subTypeFocusNode.unfocus();

      showMaterialModalBottomSheet(
        context: context,
        builder: (ctx) {
          return BudgetTypeSelector(
            budgetTypes: widget.budgetTypes,
            selectedBudgetType: _selectedBudgetType.value,
            selectedBudgetMainType: _selectedBudgetMainType.value,
            onSelectTap: (typeId) {
              _selectedBudgetType.value = typeId;
              _subTypeController.text = widget.budgetTypes.firstWhereOrNull((w) => w.id == typeId)?.name ?? "";
              _mainTypeController.text =
                  widget.budgetTypes.firstWhereOrNull((v) => v.id == widget.budgetTypes.firstWhereOrNull((w) => w.id == typeId)?.parent)?.name ?? "";
            },
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        shadowColor: Colors.white,
        surfaceTintColor: Colors.white,
        scrolledUnderElevation: 1,
        leading: IconButton(
          onPressed: () {
            if (_needUpdate) {
              showDialog(
                context: context,
                builder: (ctx) => Dialog(
                  insetPadding: EdgeInsets.all(SCAFFOLD_PADDING_HOR),
                  backgroundColor: Colors.transparent,
                  child: Container(
                    width: double.maxFinite,
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.black, width: 2),
                      color: Colors.white,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text("提示", style: Theme.of(context).textTheme.titleMedium),
                        SizedBox(height: 40),
                        Text("退出将丢失未保存的修改。"),
                        SizedBox(height: 40),
                        Row(
                          children: [
                            Expanded(
                              child: FilledButton(
                                onPressed: () {
                                  Get.back();
                                  Get.back();
                                },
                                style: ButtonStyle(backgroundColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.error)),
                                child: Text("放弃修改"),
                              ),
                            ),
                            SizedBox(width: 20),
                            Expanded(
                              child: FilledButton(
                                onPressed: () async {
                                  final r = await _save();
                                  if (r != null) {
                                    Get.back();
                                    Get.back(result: r);
                                  }
                                },
                                child: Text("保存后退出"),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              Get.back();
            }
          },
          icon: Icon(Icons.close, size: 30),
        ),
        actions: [
          IconButton(
            onPressed: () async {
              final r = await _save();
              if (r != null) {
                Get.back(result: r);
              }
            },
            icon: Text("保存"),
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(48),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: SCAFFOLD_PADDING_HOR),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.item != null)
                  IconButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (ctx) => DefaultAlertDialog(
                          title: Text("确认删除？"),
                          action: Row(
                            children: [
                              Expanded(
                                child: FilledButton(
                                  onPressed: () async {
                                    try {
                                      await api.getBudgetApi().deleteItem(ids: BuiltList([widget.item!.id]));
                                      Get.back();
                                      Get.back(result: true);
                                    } catch (ex) {
                                      Fluttertoast.showToast(msg: "删除失败，请重试");
                                    }
                                  },
                                  style: ButtonStyle(backgroundColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.error)),
                                  child: Text("删除"),
                                ),
                              ),
                              SizedBox(width: 20),
                              Expanded(
                                child: FilledButton(
                                  onPressed: () {
                                    Get.back();
                                  },
                                  child: Text("取消"),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    icon: Row(children: [Icon(Icons.delete_outline_rounded, size: 24), SizedBox(width: 4), Text("删除")]),
                  ),
                if (widget.item != null) SizedBox(width: 8),
                IconButton(
                  onPressed: () {
                    showMaterialModalBottomSheet(
                      context: context,
                      builder: (ctx) {
                        return SafeArea(
                          top: false,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ListTile(
                                title: Text("拍照"),
                                leading: Icon(CupertinoIcons.camera),
                                onTap: () async {
                                },
                              ),
                              ListTile(title: Text("相册"), leading: Icon(CupertinoIcons.photo_on_rectangle), onTap: () {}),
                              ListTile(title: Text("选择文件"), leading: Icon(CupertinoIcons.doc)),
                              ListTile(title: Text("档案库"), leading: Icon(CupertinoIcons.folder)),
                            ],
                          ),
                        );
                      },
                    );
                  },
                  icon: Row(children: [Icon(Icons.camera_alt_outlined, size: 24), SizedBox(width: 4), Text("附件")]),
                ),
                SizedBox(width: 8),
                IconButton(
                  onPressed: () async {
                    final text = await Clipboard.getData(Clipboard.kTextPlain);
                    if (text?.text == null) {
                      Fluttertoast.showToast(msg: "剪贴板中没有内容");
                      return;
                    }
                    _pasteLoading.value = true;
                    try {
                      final data = await api.getBudgetApi().parseShareURL(body: text!.text!);
                      if (data.data == null || _mergeFromPaste(data.data!) == false) {
                        Fluttertoast.showToast(msg: "没有可用信息");
                      }
                    } catch (ex) {
                      Fluttertoast.showToast(msg: "解析失败");
                    }
                  },
                  icon: Row(children: [Icon(Icons.paste_rounded, size: 24), SizedBox(width: 4), Text("粘贴")]),
                ),
                SizedBox(width: 8),
                IconButton(
                  onPressed: () {},
                  icon: Row(children: [Icon(Icons.format_list_bulleted_rounded, size: 24), SizedBox(width: 4), Text("扩展信息")]),
                ),
              ],
            ),
          ),
        ),
      ),

      body: Container(
        padding: EdgeInsets.symmetric(horizontal: SCAFFOLD_PADDING_HOR),
        child: SingleChildScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          padding: EdgeInsets.only(top: 12),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  decoration: InputDecoration(hintText: "名称"),
                  autofocus: true,
                  controller: _titleController,
                  validator: (value) {
                    if (value?.isNotEmpty == true) {
                      return null;
                    }
                    return "请输入名称";
                  },
                ),
                SizedBox(height: 20),
                // Text(""),
                // SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(hintText: "金额", prefixText: "¥", floatingLabelBehavior: FloatingLabelBehavior.always),
                        textAlign: TextAlign.right,
                        controller: _totalAmountController,
                        focusNode: _totalAmountFocus,
                        keyboardType: TextInputType.numberWithOptions(decimal: true),
                        validator: (value) {
                          if (value != null && double.tryParse(value) != null) {
                            return null;
                          }
                          return "请输入金额";
                        },
                      ),
                    ),
                    SizedBox(width: 12),
                    FilledButton(
                      onPressed: () async {
                        final amount = double.tryParse(_totalAmountController.text) ?? 0;
                        final spent = _paymentRecords.isEmpty ? 0 : _paymentRecords.map((f) => f.amount.value).reduce((a, b) => a + b);
                        final result =
                            (await showDialog(
                                  context: context,
                                  builder: (ctx) => PaymentRecord(remain: amount - spent),
                                ))
                                as BudgetPaymentValue?;
                        if (result != null) {
                          _needUpdate = true;
                          if (_manual == false && _paymentRecords.isNotEmpty) {
                            _manual = true;
                          }
                          _paymentRecords.add(result);
                        }
                      },
                      style: ButtonStyle(
                        backgroundColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.tertiary),
                        shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
                      ),
                      child: Text("添加付款"),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Theme.of(context).colorScheme.outlineVariant),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.outlineVariant,
                          borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8)),
                        ),
                        child: Row(children: [Text("付款时间"), Spacer(), Text("金额")]),
                      ),
                      ConstrainedBox(
                        constraints: BoxConstraints(minHeight: 30, maxHeight: 200),
                        child: Obx(
                          () => _paymentRecords.isEmpty
                              ? IntrinsicHeight(child: Center(child: Text("无付款记录")))
                              : SingleChildScrollView(
                                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  child: Column(
                                    children: _paymentRecords
                                        .map(
                                          (item) => GestureDetector(
                                            behavior: HitTestBehavior.translucent,
                                            onTap: () async {
                                              final amount = double.tryParse(_totalAmountController.text) ?? 0;
                                              final spent = _paymentRecords.map((f) => f.amount.value).reduce((a, b) => a + b);
                                              final result = (await showDialog(
                                                context: context,
                                                builder: (ctx) => PaymentRecord(paymentRecord: item.copy(), remain: amount - spent + item.amount.value),
                                              ));
                                              if (result == true) {
                                                _paymentRecords.remove(item);
                                              } else if (result != null) {
                                                if (_manual == false) {
                                                  _manual = true;
                                                }
                                                item.amount.value = result.amount.value;
                                                item.time.value = result.time.value;
                                                item.title.value = result.title.value;
                                                _needUpdate = true;
                                              }
                                            },
                                            child: Container(
                                              padding: EdgeInsets.symmetric(vertical: 4),
                                              child: Row(
                                                children: [
                                                  Obx(() => Text("${item.time.value.year}-${item.time.value.month}-${item.time.value.day}")),
                                                  Spacer(),
                                                  Obx(() => Text(item.amount.value.toStringAsFixed(2))),
                                                ],
                                              ),
                                            ),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(height: 40),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Text("分组", style: Theme.of(context).textTheme.titleSmall),
                          // SizedBox(height: 8),
                          TextFormField(
                            decoration: InputDecoration(hintText: "选择分组"),
                            controller: _mainTypeController,
                            focusNode: _mainTypeFocusNode,
                            validator: (value) {
                              if (value?.isNotEmpty == true) {
                                return null;
                              }
                              return "请选择分组";
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Text("类型"),
                          // SizedBox(height: 8),
                          TextFormField(
                            decoration: InputDecoration(hintText: "选择类型"),
                            controller: _subTypeController,
                            focusNode: _subTypeFocusNode,
                            validator: (value) {
                              if (value?.isNotEmpty == true) {
                                return null;
                              }
                              return "请选择类型";
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("联系人"),
                          SizedBox(height: 8),
                          TextFormField(
                            decoration: InputDecoration(hintText: " "),
                            controller: _contactNameController,
                            // textAlign: TextAlign.end,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("手机号"),
                          SizedBox(height: 8),
                          TextFormField(
                            decoration: InputDecoration(hintText: " "),
                            controller: _contactPhoneController,
                            keyboardType: TextInputType.phone,
                            // textAlign: TextAlign.end,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),
                Text("链接"),
                SizedBox(height: 8),
                TextFormField(
                  decoration: InputDecoration(hintText: " "),
                  controller: _urlController,
                  // textAlign: TextAlign.end,
                ),
                SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("宽度"),
                          SizedBox(height: 8),
                          Obx(
                            () => TextFormField(
                              controller: _sizeWidthController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                suffix: GestureDetector(onTap: _toggleSizeUnit, child: Text(_sizeUnit.value)),
                              ),
                              // textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("深度"),
                          SizedBox(height: 8),
                          Obx(
                            () => TextFormField(
                              controller: _sizeDepthController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                suffix: GestureDetector(onTap: _toggleSizeUnit, child: Text(_sizeUnit.value)),
                              ),
                              // textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("高度"),
                          SizedBox(height: 8),
                          Obx(
                            () => TextFormField(
                              controller: _sizeHeightController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                suffix: GestureDetector(onTap: _toggleSizeUnit, child: Text(_sizeUnit.value)),
                              ),
                              // textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Divider(height: 40),
                Text("备注"),
                TextFormField(minLines: 1, maxLines: 5, controller: _descriptionController),
                Divider(height: 40),
                Attachment(),
                Attachment(),
                Attachment(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _mergeFromPaste(BudgetItemParseResult data) {
    var hasValue = false;
    if (data.title != null) {
      _titleController.text = data.title ?? "";
      hasValue = true;
    }
    if (data.lineAmount != null) {
      _totalAmountController.text = data.lineAmount.toString();
      hasValue = true;
    }
    if (data.url != null) {
      _urlController.text = data.url ?? "";
      hasValue = true;
    }

    return hasValue;
  }

  Future<BudgetItemValue?> _save() async {
    if (_formKey.currentState?.validate() != true) {
      return null;
    }
    final result =
        widget.item ??
        (BudgetItemValue(null)
          ..id = generateId()
          ..budgetId = widget.budgetId);
    result.title.value = _titleController.text;
    result.totalAmount.value = double.tryParse(_totalAmountController.text);
    result.type.value = _selectedBudgetType.value;
    result.contactName.value = _contactNameController.text;
    result.contactPhone.value = _contactPhoneController.text;
    result.url.value = _urlController.text;
    result.sizeWidth.value = int.tryParse(_sizeWidthController.text);
    result.sizeHeight.value = int.tryParse(_sizeHeightController.text);
    result.sizeDepth.value = int.tryParse(_sizeDepthController.text);
    result.description.value = _descriptionController.text;
    result.paymentRecords.value = _paymentRecords;
    final success = await api.getBudgetApi().upsertItem(tbBudgetItem: BuiltList([result.toDb()]));
    if (success.data == true) {
      return result;
    } else {
      Fluttertoast.showToast(msg: "提交错误，请重试");
      return null;
    }
  }

  void _toggleSizeUnit() {
    if (_sizeUnit.value == "mm") {
      _sizeUnit.value = "cm";
    } else if (_sizeUnit.value == "cm") {
      _sizeUnit.value = "m";
    } else if (_sizeUnit.value == "m") {
      _sizeUnit.value = "mm";
    }
  }
}
