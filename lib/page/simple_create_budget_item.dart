import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:openapi/openapi.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:zxb/component/budget_type_selector.dart';
import 'package:zxb/component/default_camera.dart';
import 'package:zxb/consts.dart';

class SimpleCreateBudgetItem extends StatefulWidget {
  const SimpleCreateBudgetItem({super.key});

  @override
  State<StatefulWidget> createState() => _SimpleCreateBudgetItemState();
}

class _SimpleCreateBudgetItemState extends State<SimpleCreateBudgetItem> {
  final _selectedCategory = Rxn<TbBudgetType>();
  final _selectedType = Rxn<TbBudgetType>();
  final _budgetTypes = <TbBudgetType>[].obs;

  @override
  void initState() {
    super.initState();
    api.getBudgetTypeApi().getBudgetTypes().then((data) {
      _budgetTypes.value = data.data?.toList() ?? [];
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: SCAFFOLD_PADDING_HOR),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 40),
            GestureDetector(
              onTap: () {
                showMaterialModalBottomSheet(
                  context: context,
                  builder: (ctx) => BudgetTypeSelector(
                    budgetTypes: _budgetTypes,
                    onSelectTap: (id) {
                      _selectedType.value = _budgetTypes.firstWhere((item) => item.id == id);
                      _selectedCategory.value = _budgetTypes.firstWhere((item) => item.id == _selectedType.value?.parent);
                    },
                    selectedBudgetMainType: _selectedCategory.value?.id,
                    selectedBudgetType: _selectedType.value?.id,
                  ),
                );
              },
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 41,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade500),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 12),
                      alignment: Alignment.centerLeft,
                      child: Obx(() => Text(_selectedCategory.value == null ? "请选择分组" : _selectedCategory.value!.name)),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade500),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      alignment: Alignment.centerLeft,
                      child: Obx(() => Text(_selectedType.value == null ? "请选择类型" : _selectedType.value!.name)),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: ButtonStyle(minimumSize: WidgetStatePropertyAll(Size(double.maxFinite, 50))),
                    onPressed: () {},
                    child: Text("添加预算项"),
                  ),
                ),
              ],
            ),
            Divider(height: 40),
            createButton("粘贴文字", "商品名称、电商分享链接", Icons.paste),
            SizedBox(height: 12),
            createButton("上传文件", "订单截图、报价单", CupertinoIcons.doc),
            SizedBox(height: 12),
            createButton("拍照扫描", "报价单、合同", Icons.document_scanner_outlined),
          ],
        ),
      ),
    );
  }

  Widget createButton(String title, String description, IconData icon) => SizedBox(
    height: 50,
    child: FilledButton(
      onPressed: () async {
        showMaterialModalBottomSheet(context: context, builder: (ctx) => DefaultCamera());
      },
      style: ButtonStyle(
        backgroundColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.surface),
        padding: WidgetStatePropertyAll(EdgeInsets.all(4)),
        side: WidgetStatePropertyAll(BorderSide(color: Theme.of(context).colorScheme.tertiaryContainer)),
        overlayColor: WidgetStatePropertyAll(Theme.of(context).colorScheme.tertiaryContainer.withAlpha(10)),
      ),
      child: Stack(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: 42,
                height: 42,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(99999), color: Theme.of(context).colorScheme.tertiaryContainer),
                child: Icon(icon, color: Theme.of(context).colorScheme.onTertiaryContainer, size: 26),
              ),
            ],
          ),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(title, style: Theme.of(context).textTheme.titleSmall?.copyWith(color: Theme.of(context).colorScheme.tertiaryContainer)),
                Text(description, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.tertiaryContainer)),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
