import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/page/home.dart';
import 'package:zxb/security.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<StatefulWidget> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final _phoneController = TextEditingController();
  final _codeController = TextEditingController();
  final _codeFocus = FocusNode();
  final _phoneKey = GlobalKey<FormFieldState>();
  final _formKey = GlobalKey<FormState>();
  Timer? _smsCountdownTimer;
  final _smsCountdown = Rxn<int>();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: SCAFFOLD_PADDING_HOR),
            child: Column(
              children: [
                Container(height: 200, alignment: Alignment.center, child: Text("Logo")),
                TextFormField(
                  key: _phoneKey,
                  decoration: InputDecoration(
                    hintText: "手机号",
                    suffix: Obx(
                      () => GestureDetector(
                        onTap: _smsCountdown.value != null
                            ? null
                            : () async {
                                HapticFeedback.heavyImpact();
                                final x = _phoneKey.currentState?.validate();
                                if (x == true) {
                                  _smsCountdown.value = null;
                                  _smsCountdownTimer?.cancel();
                                  final response = (await api.getSmsApi().sendCode(
                                    smsRequest: (SmsRequestBuilder()..phone = _phoneController.text).build(),
                                  )).data;
                                  if (response?.success == true) {
                                    _codeFocus.requestFocus();
                                  }
                                  if (response?.message != null) {
                                    Fluttertoast.showToast(msg: response!.message!, gravity: ToastGravity.CENTER);
                                  }
                                  if (response?.expire != null) {
                                    _smsCountdown.value = response!.expire!;
                                    _smsCountdownTimer = Timer.periodic(Duration(seconds: 1), (t) {
                                      _smsCountdown.value = _smsCountdown.value! - 1;
                                      if (_smsCountdown.value! <= 0) {
                                        _smsCountdown.value = null;
                                        t.cancel();
                                      }
                                    });
                                  }
                                }
                              },
                        child: () {
                          return _smsCountdown.value != null ? Text(_smsCountdown.toString()) : Text("获取验证码");
                        }(),
                      ),
                    ),
                  ),
                  autofocus: true,
                  controller: _phoneController,
                  // focusNode: _phoneFocusNode,
                  keyboardType: TextInputType.phone,
                  validator: (v) {
                    if (v?.isNotEmpty == true) {
                      return null;
                    }
                    return "请输入手机号";
                  },
                ),
                SizedBox(height: 20),
                TextFormField(
                  decoration: InputDecoration(hintText: "验证码"),
                  controller: _codeController,
                  focusNode: _codeFocus,
                  keyboardType: TextInputType.number,
                  validator: (v) {
                    if (v?.isNotEmpty == true) {
                      return null;
                    }
                    return "请输入验证码";
                  },
                ),
                SizedBox(height: 40),
                SizedBox(
                  width: double.maxFinite,
                  child: FilledButton(
                    onPressed: () async {
                      if (_formKey.currentState?.validate() == true) {
                        final response = await api.getSmsApi().login(
                          smsVerifyRequest:
                              (SmsVerifyRequestBuilder()
                                    ..phone = _phoneController.text
                                    ..code = _codeController.text)
                                  .build(),
                        );
                        if (response.data != null && response.data?.success == true) {
                          await setToken(response.data!.token!);
                          CURRENT_USER.value = (await api.getUserControllerApi().getCurrentUser()).data;
                          await Get.offAll(() => Home());
                          return;
                        }
                        if (response.data?.message != null) {
                          Fluttertoast.showToast(msg: response.data!.message!, gravity: ToastGravity.CENTER);
                        }
                      }
                    },
                    child: Text("登录"),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
