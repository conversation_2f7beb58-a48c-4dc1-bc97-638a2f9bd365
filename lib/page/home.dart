import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/consts.dart';
import 'package:zxb/page/budget.dart';
import 'package:zxb/page/project.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<StatefulWidget> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  final _selectedIndex = 0.obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (_selectedIndex.value == 0) {
          return FutureBuilder(
            future: api.getBudgetApi().getDefaultBudget(),
            builder: (ctx, snp) {
              if (snp.connectionState != ConnectionState.done) {
                return Center(child: CircularProgressIndicator());
              } else {
                if (snp.data?.data == null) {
                  return FutureBuilder(
                    future: api.getBudgetApi().createBudget(createBudgetRequest: (CreateBudgetRequestBuilder()..name = "").build()),
                    builder: (ctx2, snp2) {
                      if (snp2.connectionState != ConnectionState.done) {
                        return Center(child: CircularProgressIndicator());
                      } else {
                        return Budget(budgetId: snp2.data!.data!);
                      }
                    },
                  );
                } else {
                  return Budget(budgetId: snp.data!.data!.id!);
                }
              }
            },
          );
        } else if (_selectedIndex.value == 2) {
          return Center(
            child: Obx(
              () => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text(CURRENT_USER.value?.name ?? ""), Text(CURRENT_USER.value?.isanonymous == false ? "" : "匿名用户")],
              ),
            ),
          );
        }
        return SizedBox.shrink();
      }),
      bottomNavigationBar: Obx(
        () => BottomNavigationBar(
          currentIndex: _selectedIndex.value,
          onTap: (i) {
            HapticFeedback.selectionClick();
            _selectedIndex.value = i;
          },
          type: BottomNavigationBarType.fixed,
          enableFeedback: true,
          items: [
            BottomNavigationBarItem(icon: Icon(Icons.home_rounded), label: "首页"),
            // BottomNavigationBarItem(icon: Icon(Icons.business), label: "项目"),
            BottomNavigationBarItem(icon: Icon(Icons.person), label: "我的"),
          ],
        ),
      ),
    );
  }
}
