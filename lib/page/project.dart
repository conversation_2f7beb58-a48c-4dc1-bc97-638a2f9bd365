import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zxb/page/budget.dart';

class Project extends StatefulWidget {
  const Project({super.key});

  @override
  State<StatefulWidget> createState() => _ProjectState();
}

class _ProjectState extends State<Project> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(title: Text("项目名称"), pinned: false),
          SliverOverlapAbsorber(
            handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            sliver: SliverP<PERSON><PERSON>entHeader(delegate: _TabBarHeader(_tabController), pinned: true, floating: true),
          ),
        ],
        body: NotificationListener<ScrollStartNotification>(
          onNotification: (notice) {
            if (notice.metrics.axis == Axis.horizontal) {
              HapticFeedback.selectionClick();
            }
            return false;
          },
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildTabContent(Text("1")),
              _buildTabContent(Budget(budgetId: "a")),
              _buildTabContent(Text("1")),
              _buildTabContent(Text("1")),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent(Widget content) {
    return Builder(
      builder: (context) => CustomScrollView(
        slivers: [
          SliverOverlapInjector(handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context)),
          SliverToBoxAdapter(child: content),
        ],
      ),
    );
  }
}

class _TabBarHeader extends SliverPersistentHeaderDelegate {
  TabController controller;

  _TabBarHeader(this.controller);

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      color: Theme.of(context).scaffoldBackgroundColor,
      elevation: overlapsContent ? 1 : 0, // 根据是否重叠添加阴影
      child: SizedBox(
        height: 30,
        child: TabBar(
          tabs: [
            IntrinsicWidth(
              child: Container(alignment: Alignment.center, height: 40, child: Text("项目进度")),
            ),
            IntrinsicWidth(
              child: Container(alignment: Alignment.center, height: 40, child: Text("预算清单")),
            ),
            IntrinsicWidth(
              child: Container(alignment: Alignment.center, height: 40, child: Text("团队成员")),
            ),
            IntrinsicWidth(
              child: Container(alignment: Alignment.center, height: 40, child: Text("项目信息")),
            ),
          ],
          controller: controller,
          enableFeedback: true,
        ),
      ),
    );
  }

  @override
  double get maxExtent => 30;

  @override
  double get minExtent => 30;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
