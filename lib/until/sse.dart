import 'dart:convert';

import 'package:built_value/serializer.dart';
import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/consts.dart';

void sseRequest({required String path, required Object data, required List<dynamic> types, required Function(dynamic object) callback}) async {
  final resp = await dio.post(
    path,
    data: data,
    options: Options(
      responseType: ResponseType.stream,
      headers: {
        'Accept': 'text/event-stream', // SSE 标准头
        'Cache-Control': 'no-cache',
        "Content-Type": "application/json"
      },
    ),
  );
  // _serializers.deserialize(
  //   rawResponse,
  //   specifiedType: const FullType(BuiltList, [FullType(Location)]),
  // ) as BuiltList<Location>

  (resp.data as ResponseBody).stream.cast<List<int>>().transform(utf8.decoder).transform(const LineSplitter()).listen((line) {
    if (!line.startsWith("data:")) {
      return;
    }
    final str = line.substring(5);
    for (var type in types) {
      try {
        if (type is FullType) {
          final v = standardSerializers.deserialize(jsonDecode(str), specifiedType: type);
          callback(v);
        } else {
          final v = standardSerializers.deserialize(jsonDecode(str), specifiedType: FullType(type));
          callback(v);
        }
        break;
      } catch (e) {
        print(e);
      }
    }
  });
}
