import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/security.dart';

late Openapi api;
late Dio dio;

Future<void> initApi() async {
  // final iosInfo = await DeviceInfoPlugin().iosInfo;
  // final isSimulator = !iosInfo.isPhysicalDevice;

  // 在Debug模式下使用不同的配置
  // String baseUrl = isSimulator ? "http://127.0.0.1:8080" : "http://fooevr.local:8080";
  // String baseUrl = "https://api.renovation.xin";
  String baseUrl = "http://fooevr.local:8080";

  dio = Dio(BaseOptions(baseUrl: baseUrl));

  dio.interceptors.add(AuthenticationInterceptor());
  api = Openapi(dio: dio);
}

const SCAFFOLD_PADDING_HOR = 24.0;
const ITEM_SIZE = 50.0;

Fluwx FLUWX = Fluwx();
final CURRENT_USER = Rxn<TbUser>();
