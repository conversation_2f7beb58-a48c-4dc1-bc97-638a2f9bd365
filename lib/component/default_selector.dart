import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:zxb/consts.dart';

class DefaultSelector extends StatefulWidget {
  final List<String> items;
  final Function(int index) onChanged;
  final int initSelectedIndex;

  const DefaultSelector({super.key, required this.items, required this.onChanged, required this.initSelectedIndex});

  @override
  State<StatefulWidget> createState() => _DefaultSelectorState();
}

class _DefaultSelectorState extends State<DefaultSelector> {
  late FixedExtentScrollController controller;

  @override
  void initState() {
    super.initState();
    controller = FixedExtentScrollController(initialItem: widget.initSelectedIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      padding: EdgeInsets.only(top: 20, bottom: 20),
      child: CupertinoPicker(
        onSelectedItemChanged: (idx) async {
          widget.onChanged(idx);
        },
        scrollController: controller,
        looping: false,
        itemExtent: 30,
        squeeze: 1.2,
        magnification: 1.22,
        useMagnifier: true,
        children: widget.items.map((item) => Center(child: Text(item, style: Theme.of(context).textTheme.bodyMedium))).toList(),
      ),
    );
  }
}
