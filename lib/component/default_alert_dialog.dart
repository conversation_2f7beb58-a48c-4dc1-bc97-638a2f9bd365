import 'package:flutter/material.dart';
import 'package:zxb/consts.dart';

class DefaultAlertDialog extends StatelessWidget {
  final Widget? title;
  final Widget? content;
  final Widget action;

  const DefaultAlertDialog({super.key, this.title, this.content, required this.action});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.all(SCAFFOLD_PADDING_HOR),
      child: Container(
        width: double.maxFinite,
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.black, width: 2),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null) DefaultTextStyle(style: Theme.of(context).textTheme.titleLarge!, child: title!),
            if (content != null) content! else SizedBox(height: 20),
            SizedBox(height: 20),
            action,
          ],
        ),
      ),
    );
  }
}
