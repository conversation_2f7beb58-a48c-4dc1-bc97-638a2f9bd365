import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/consts.dart';

class RegionSelector extends StatefulWidget {
  final String province;
  final String city;
  final Function(TbRegion) onSelectedProvince;
  final Function(TbRegion) onSelectedCity;

  const RegionSelector({super.key, required this.province, required this.city, required this.onSelectedProvince, required this.onSelectedCity});

  @override
  State<StatefulWidget> createState() => _RegionSelectorState();
}

class _RegionSelectorState extends State<RegionSelector> {
  List<TbRegion>? _provinces;
  List<TbRegion>? _cities;
  var inited = false;
  final _provinceController = Rxn<FixedExtentScrollController>();
  final _cityController = Rxn<FixedExtentScrollController>();

  @override
  void initState() {
    super.initState();
    api.getLocationApi().getRegionsByParentId(parentId: "100000").then((data) {
      _provinces = data.data?.toList().sortedBy((v) => v.id);
      if (data.data?.isNotEmpty == true) {
        var index = 0;
        if (widget.province != null) {
          index = _provinces?.indexWhere((v) => v.id == widget.province) ?? 0;
        }
        _provinceController.value = FixedExtentScrollController(initialItem: index);
        _provinceChange(index);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        SizedBox(
          height: 200,
          width: 150,
          child: Obx(
            () => _provinceController.value == null
                ? SizedBox.shrink()
                : CupertinoPicker(
                    onSelectedItemChanged: (idx) async {
                      _provinceChange(idx);
                    },
                    scrollController: _provinceController.value!,
                    looping: false,
                    itemExtent: 30,
                    squeeze: 1.2,
                    magnification: 1.22,
                    useMagnifier: true,
                    children: _provinces?.map((item) => Center(child: Text(item.name, style: Theme.of(context).textTheme.titleSmall))).toList() ?? [],
                  ),
          ),
        ),
        //_cities.value = (await api.getLocationApi().getRegionsByParentId(parentId: _provinces.first.id)).data?.toList() ?? [];
        SizedBox(
          width: 150,
          height: 200,
          child: Obx(() {
            return _cityController.value == null
                ? SizedBox.shrink()
                : CupertinoPicker(
                    onSelectedItemChanged: (idx) {
                      inited = true;
                      widget.onSelectedCity(_cities![idx]);
                    },
                    looping: false,
                    itemExtent: 30,
                    squeeze: 1.2,
                    magnification: 1.22,
                    useMagnifier: true,
                    scrollController: _cityController.value!,
                    children: _cities!.map((item) => Center(child: Text(item.name, style: Theme.of(context).textTheme.titleSmall))).toList(),
                  );
          }),
        ),
      ],
    );
  }

  void _provinceChange(int index) async {
    widget.onSelectedProvince(_provinces![index]);
    print('change data source');
    _cities = (await api.getLocationApi().getRegionsByParentId(parentId: _provinces![index].id)).data?.toList().sortedBy((v) => v.id);

    var idx = inited ? 0 : _cities!.indexWhere((v) => v.id == widget.city);
    print('change controller, $idx');
    _cityController.value?.animateToItem(idx, duration: Duration(milliseconds: 100), curve: Curves.linear);
    _cityController.value = FixedExtentScrollController(initialItem: idx);
    widget.onSelectedCity(_cities![idx]);
    inited = true;
  }
}
