import 'package:flutter/material.dart';

class DefaultCamera extends StatefulWidget {
  const DefaultCamera({super.key});

  @override
  State<StatefulWidget> createState() => _DefaultCameraState();
}

class _DefaultCameraState extends State<DefaultCamera> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    throw UnimplementedError();
  }
}
