import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:zxb/consts.dart';

class RoomLayoutSelector extends StatefulWidget {
  final Map<String, int> roomLayout;
  final Function(String roomId, int roomCount) onChanged;

  const RoomLayoutSelector({super.key, required this.roomLayout, required this.onChanged});

  @override
  State<StatefulWidget> createState() => _RoomLayoutSelectorState();
}

class _RoomLayoutSelectorState extends State<RoomLayoutSelector> {
  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return FutureBuilder(
      future: api.getBasicApi().getRoomTypes(),
      builder: (cts, snp) {
        if (snp.connectionState != ConnectionState.done) {
          return SizedBox(height: 200, child: Center(child: CircularProgressIndicator()));
        }
        final rooms = (snp.data?.data?.toList() ?? []).where((item) => item.defaultVisiable == true).toList().sortedBy((v) => v.index!);
        final controllers = rooms.map((item) {
          return FixedExtentScrollController(
            initialItem: (widget.roomLayout.containsKey(item.id) ? widget.roomLayout[item.id]! : (item.defaultCount ?? 0)),
          );
        }).toList();
        return Padding(
          padding: EdgeInsets.only(top: 20, bottom: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: rooms.map((item) {
                  return SizedBox(
                    width: width / (rooms.isEmpty ? 1 : rooms.length),
                    child: Center(child: Text(item.name.toString(), style: Theme.of(context).textTheme.titleSmall)),
                  );
                }).toList(),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: rooms.map((item) {
                  return SizedBox(
                    height: 200,
                    width: width / (rooms.isEmpty ? 1 : rooms.length),
                    child: CupertinoPicker(
                      onSelectedItemChanged: (idx) async {
                        widget.onChanged(item.id!, idx);
                      },
                      scrollController: controllers[rooms.indexOf(item)],
                      looping: true,
                      itemExtent: 30,
                      squeeze: 1.2,
                      magnification: 1.22,
                      useMagnifier: true,
                      children: List.generate(9, (idx) {
                        return Center(child: Text((idx).toString() + item.shortName, style: Theme.of(context).textTheme.bodyMedium));
                      }),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }
}
