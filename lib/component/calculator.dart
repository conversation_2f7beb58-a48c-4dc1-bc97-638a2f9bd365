import 'package:expressions/expressions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class Calculator {
  final BuildContext context;

  Calculator(this.context);

  OverlayEntry? _overlayEntry;

  void showKeyboardToolbar(TextEditingController controller) {
    hideKeyboardToolbar(); // 先移除可能存在的overlay
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: 0,
        right: 0,
        bottom: MediaQuery.of(context).viewInsets.bottom,
        child: Material(
          elevation: 8,
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(top: BorderSide(color: Theme.of(context).colorScheme.outline, width: 0.5)),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: FilledButton(
                    onPressed: () {
                      controller.text += "+";
                    },
                    style: FilledButton.styleFrom(backgroundColor: Colors.transparent, overlayColor: Colors.grey.withAlpha(80)),
                    child: SvgPicture.asset("assets/icon/add.svg", width: 16, height: 16),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: FilledButton(
                    onPressed: () {
                      controller.text += "-";
                    },
                    style: FilledButton.styleFrom(backgroundColor: Colors.transparent, overlayColor: Colors.grey.withAlpha(80)),
                    child: SvgPicture.asset("assets/icon/minus.svg", width: 16, height: 16),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: FilledButton(
                    onPressed: () {
                      controller.text += "*";
                    },
                    style: FilledButton.styleFrom(backgroundColor: Colors.transparent, overlayColor: Colors.grey.withAlpha(80)),
                    child: SvgPicture.asset("assets/icon/multiply.svg", width: 16, height: 16),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: FilledButton(
                    onPressed: () {
                      controller.text += "/";
                    },
                    style: FilledButton.styleFrom(backgroundColor: Colors.transparent, overlayColor: Colors.grey.withAlpha(80)),
                    child: SvgPicture.asset("assets/icon/divide.svg", width: 16, height: 16),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: FilledButton(
                    onPressed: () {
                      final exp = Expression.tryParse(controller.text);
                      if (exp == null) {
                        return;
                      }
                      final calc = ExpressionEvaluator();
                      final result = calc.eval(exp, {});
                      controller.text = (result as num).toString();
                    },
                    style: FilledButton.styleFrom(backgroundColor: Colors.transparent, overlayColor: Colors.grey.withAlpha(80)),
                    child: SvgPicture.asset("assets/icon/equal.svg", width: 16, height: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void hideKeyboardToolbar() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
