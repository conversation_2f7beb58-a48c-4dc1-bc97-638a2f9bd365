import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openapi/src/model/tb_budget_type.dart';
import 'package:zxb/consts.dart';

class BudgetTypeSelector extends StatefulWidget {
  final List<TbBudgetType> budgetTypes;
  final String? selectedBudgetMainType;
  final String? selectedBudgetType;
  final Function(String selectedBudgetTypeID) onSelectTap;

  const BudgetTypeSelector({super.key, required this.budgetTypes, required this.onSelectTap, this.selectedBudgetType, this.selectedBudgetMainType});

  @override
  State<StatefulWidget> createState() => _BudgetTypeSelectorState();
}

class _BudgetTypeSelectorState extends State<BudgetTypeSelector> with TickerProviderStateMixin {
  late TabController _controller;
  late List<TbBudgetType> _mainTypes;
  late Map<String, List<TbBudgetType>> _subTypes;
  late Rxn<TbBudgetType> _selectedMainType;
  late Rxn<TbBudgetType> _selectedSubType;

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: 2, vsync: this);
    _mainTypes = widget.budgetTypes.where((p) => p.parent == null).toList(growable: false);
    _subTypes = groupBy(widget.budgetTypes.where((p) => p.parent != null), (v) => v.parent!);
    _selectedSubType = Rxn<TbBudgetType>(widget.budgetTypes.firstWhereOrNull((v) => v.id == widget.selectedBudgetType));
    _selectedMainType = Rxn<TbBudgetType>(widget.budgetTypes.firstWhereOrNull((v) => v.id == _selectedSubType.value?.parent));
    if (widget.selectedBudgetType == null && widget.selectedBudgetMainType != null) {
      _selectedMainType.value = widget.budgetTypes.firstWhereOrNull((v)=>v.id == widget.selectedBudgetMainType);
      _controller.animateTo(1);
    }
    _mainTypes.sortBy((a) => a.index!);
    _subTypes.forEach((_, v) {
      v.sortBy((a) => a.index!);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(height: 400,child: Column(
      children: [
        TabBar(
          controller: _controller,
          tabs: [
            Container(alignment: Alignment.center, height: 40, child: Text("分组")),
            Container(alignment: Alignment.center, height: 40, child: Text("类型")),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _controller,
            children: [
              ListView.builder(
                itemBuilder: (ctx, idx) {
                  final item = _mainTypes[idx];
                  return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    child: Row(
                      children: [
                        Icon(Icons.ac_unit_outlined),
                        SizedBox(width: 12, height: 40),
                        Text(item.name),
                        Spacer(),
                        Obx(
                              () => Visibility(
                            visible: _selectedMainType.value?.id == item.id,
                            child: Icon(Icons.check, color: Theme.of(context).colorScheme.primary),
                          ),
                        ),
                      ],
                    ),
                    onTap: () {
                      _selectedMainType.value = item;
                      _controller.animateTo(1);
                    },
                  );
                },
                itemCount: _mainTypes.length,
                padding: EdgeInsets.only(left: SCAFFOLD_PADDING_HOR, right: SCAFFOLD_PADDING_HOR, top: 12, bottom: 0),
              ),
              Obx(
                    () => _selectedMainType.value == null
                    ? SizedBox.shrink()
                    : ListView.builder(
                  padding: EdgeInsets.only(left: SCAFFOLD_PADDING_HOR, right: SCAFFOLD_PADDING_HOR, top: 12, bottom: 0),
                  itemBuilder: (ctx, idx) {
                    final item = _subTypes[_selectedMainType.value!.id]![idx];
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      child: Row(
                        children: [
                          Icon(Icons.ac_unit_outlined),
                          SizedBox(width: 12, height: 40),
                          Text(item.name),
                          Spacer(),
                          Obx(
                                () => Visibility(
                              visible: _selectedSubType.value?.id == item.id,
                              child: Icon(Icons.check, color: Theme.of(context).colorScheme.primary),
                            ),
                          ),
                        ],
                      ),
                      onTap: () {
                        _selectedSubType.value = item;
                        widget.onSelectTap(item.id!);
                        Get.back();
                      },
                    );
                  },
                  itemCount: _subTypes[_selectedMainType.value!.id]!.length,
                ),
              ),
            ],
          ),
        ),
      ],
    ),);
  }
}
