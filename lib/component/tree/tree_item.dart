import 'package:get/get.dart';

class TreeItem<T> {
  TreeItem<T>? parent;

  final RxList<TreeItem<T>> _children = RxList<TreeItem<T>>();
  T data;
  Object id;
  RxnBool checked = RxnBool(false);

  TreeItem({required this.id, required this.data});

  bool get isLeaf => _children.isEmpty;

  List<TreeItem<T>> get children => _children.value;

  int get index => parent?._children.indexOf(this) ?? -1;

  void insertChild(int index, TreeItem<T> node) {
    // Adjust the index if necessary when dropping a node at the same parent
    if (node.parent == this && node.index < index) {
      index--;
    }

    // Ensure the node is removed from its previous parent and update it
    node
      ..parent?._children.remove(node)
      ..parent = this;
    _children.insert(index, node);

    if (children.every((v) => v.checked.value == true)) {
      checked.value = true;
    } else if (children.every((v) => v.checked.value == false)) {
      checked.value = false;
    } else {
      checked.value = null;
    }
  }

  void appendChild(TreeItem<T> node) {
    node.parent = this;
    _children.add(node);
    if (children.every((v) => v.checked.value == true)) {
      checked.value = true;
    } else if (children.every((v) => v.checked.value == false)) {
      checked.value = false;
    } else {
      checked.value = null;
    }
  }

  void check(bool value) {
    checked.value = value;
    for (var e in _children) {
      e.check(value);
    }
    _updateParent();
  }

  void _updateParent() {
    if (parent != null) {
      bool? newParentValue;
      if (parent!.children.every((v) => v.checked.value == true)) {
        newParentValue = true;
      } else if (parent!.children.every((v) => v.checked.value == false)) {
        newParentValue = false;
      } else {
        newParentValue = null;
      }

      // 只有当父节点状态发生变化时才继续向上传导
      if (parent!.checked.value != newParentValue) {
        parent!.checked.value = newParentValue;
        parent!._updateParent(); // 递归调用，继续向上传导
      }
    }
  }
}
