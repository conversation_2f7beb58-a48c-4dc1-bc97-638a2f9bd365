import 'package:flutter/material.dart';
import 'package:flutter_fancy_tree_view2/flutter_fancy_tree_view2.dart';
import 'package:get/get.dart';
import 'package:zxb/component/tree/tree_drag_and_drop_details.dart';
import 'package:zxb/component/tree/tree_item.dart';

import 'drag_and_drop_tree_tile.dart';

class DragAndDropTreeView<T> extends StatefulWidget {
  final Function(T item, bool isReorder)? saveItem;
  final Function(T? item)? onSelectedItem;
  final Widget Function(int level, TreeItem<T> item) itemBuilder;
  final TreeController<TreeItem<T>> controller;
  final TreeItem<T> root;

  /// When [onSelectedItem] is null, tap item will expand or collapse the item.
  const DragAndDropTreeView({super.key, this.saveItem, this.onSelectedItem, required this.itemBuilder, required this.controller, required this.root});

  @override
  State<DragAndDropTreeView<T>> createState() => _DragAndDropTreeViewState<T>();
}

class _DragAndDropTreeViewState<T> extends State<DragAndDropTreeView<T>> {
  final selectedId = Rx<Object?>(null);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void onNodeAccepted(TreeDragAndDropDetails<TreeItem<T>> details) {
    TreeItem<T>? newParent;
    int newIndex = 0;

    bool isReorder = false;
    details.mapDropPosition(
      whenAbove: () {
        newParent = details.targetNode.parent;
        newIndex = details.targetNode.index;
        isReorder = true;
      },
      whenInside: () {
        newParent = details.targetNode;
        newIndex = details.targetNode.children.length;
        widget.controller.setExpansionState(details.targetNode, true);
      },
      whenBelow: () {
        newParent = details.targetNode.parent;
        newIndex = details.targetNode.index + 1;
        isReorder = true;
      },
    );

    (newParent ?? widget.root).insertChild(newIndex, details.draggedNode);
    widget.saveItem?.call(details.draggedNode.data, isReorder);
    // Rebuild the tree to show the reordered node in its new vicinity.
    widget.controller.rebuild();
  }

  Duration? longPressDelay;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    longPressDelay = switch (Theme.of(context).platform) {
      TargetPlatform.android || TargetPlatform.fuchsia || TargetPlatform.iOS => Durations.long2,
      TargetPlatform.linux || TargetPlatform.macOS || TargetPlatform.windows => null,
    };
  }

  @override
  Widget build(BuildContext context) {
    final IndentGuide indentGuide = DefaultIndentGuide.of(context);
    final BorderSide borderSide = BorderSide(
      color: Theme.of(context).colorScheme.outline,
      width: indentGuide is AbstractLineGuide ? indentGuide.thickness : 2.0,
    );
    return AnimatedTreeView<TreeItem<T>>(
      treeController: widget.controller,
      padding: EdgeInsets.only(bottom: 0),
      nodeBuilder: (BuildContext context, TreeEntry<TreeItem<T>> entry) {
        return Obx(
          () => DragAndDropTreeTile<T>(
            entry: entry,
            borderSide: borderSide,
            longPressDelay: longPressDelay,
            onNodeAccepted: onNodeAccepted,
            onFolderPressed: () => widget.controller.toggleExpansion(entry.node),
            onSelectedItem: (item) {
              if (widget.onSelectedItem != null) {
                if (selectedId.value == item.id) {
                  selectedId.value = null;
                  widget.onSelectedItem!(null);
                } else {
                  selectedId.value = item.id;
                  widget.onSelectedItem!(item.data);
                }
              } else {
                widget.controller.toggleExpansion(item);
              }
            },
            isSelected: selectedId.value == entry.node.id,
            child: Row(
              children: [
                SizedBox(width: 8),
                SizedBox(
                  width: 24,
                  height: 24,
                  child: Obx(
                    () => Checkbox(
                      visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                      tristate: true,
                      value: entry.node.checked.value,
                      onChanged: (v) {
                        entry.node.check(v == true);
                      },
                    ),
                  ),
                ),
                SizedBox(width: 8),
                widget.itemBuilder(entry.level, entry.node),
              ],
            ),
          ),
        );
      },
      duration: Duration(milliseconds: 100),
    );
  }
}
