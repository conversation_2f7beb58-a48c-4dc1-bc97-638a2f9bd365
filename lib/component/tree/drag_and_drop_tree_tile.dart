import 'package:flutter/material.dart';
import 'package:flutter_fancy_tree_view2/flutter_fancy_tree_view2.dart';
import 'package:zxb/component/tree/tree_drag_and_drop_details.dart';
import 'package:zxb/component/tree/tree_item.dart';
import 'package:zxb/component/tree/tree_tile.dart';


class DragAndDropTreeTile<T> extends StatelessWidget {
  const DragAndDropTreeTile({
    super.key,
    required this.entry,
    required this.onNodeAccepted,
    required this.child,
    this.borderSide = BorderSide.none,
    this.longPressDelay,
    this.onFolderPressed,
    required this.onSelectedItem,
    required this.isSelected,
  });

  final TreeEntry<TreeItem<T>> entry;
  final TreeDragTargetNodeAccepted<TreeItem<T>> onNodeAccepted;
  final BorderSide borderSide;
  final Duration? longPressDelay;
  final VoidCallback? onFolderPressed;
  final Widget child;
  final Function(TreeItem<T> item) onSelectedItem;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return TreeDragTarget<TreeItem<T>>(
      node: entry.node,
      onNodeAccepted: onNodeAccepted,
      builder: (BuildContext context, TreeDragAndDropDetails<TreeItem<T>>? details) {
        Decoration? decoration;

        if (details != null) {
          // Add a border to indicate in which portion of the target's height
          // the dragging node will be inserted.
          decoration = BoxDecoration(
            border: details.mapDropPosition(
              whenAbove: () => Border(top: borderSide),
              whenInside: () => Border.fromBorderSide(borderSide),
              whenBelow: () => Border(bottom: borderSide),
            ),
          );
        }

        return TreeDraggable<TreeItem<T>>(
          node: entry.node,
          longPressDelay: longPressDelay,
          childWhenDragging: Opacity(
            opacity: .5,
            child: IgnorePointer(
              child: TreeTile<T>(entry: entry, onSelectedItem: onSelectedItem, isSelected: isSelected, child: child),
            ),
          ),
          feedback: IntrinsicWidth(
            child: Material(
              elevation: 4,
              child: TreeTile<T>(
                entry: entry,
                showIndentation: false,
                onFolderPressed: () {},
                onSelectedItem: onSelectedItem,
                isSelected: isSelected,
                child: child,
              ),
            ),
          ),
          child: TreeTile<T>(
            entry: entry,
            onFolderPressed: entry.node.isLeaf ? null : onFolderPressed,
            decoration: decoration,
            onSelectedItem: onSelectedItem,
            isSelected: isSelected,
            child: child,
          ),
        );
      },
    );
  }
}
