import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_fancy_tree_view2/flutter_fancy_tree_view2.dart';
import 'package:zxb/component/tree/tree_item.dart';

class TreeTile<T> extends StatelessWidget {
  const TreeTile({
    super.key,
    required this.entry,
    this.onFolderPressed,
    this.decoration,
    this.showIndentation = true,
    required this.child,
    required this.onSelectedItem,
    required this.isSelected,
  });

  final TreeEntry<TreeItem<T>> entry;
  final VoidCallback? onFolderPressed;
  final Decoration? decoration;
  final bool showIndentation;
  final Widget child;
  final Function(TreeItem<T> item) onSelectedItem;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    Widget content = Padding(
      padding: const EdgeInsetsDirectional.only(end: 0, start: 0),
      child: Row(
        children: [
          Visibility(
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            visible: !entry.node.isLeaf,
            child: GestureDetector(
              onTap: onFolderPressed,
              behavior: HitTestBehavior.translucent,
              child: AnimatedRotation(
                duration: Duration(milliseconds: 100),
                turns: entry.isExpanded ? .25 : 0,
                child: Icon(CupertinoIcons.play_fill, size: 16, color: Colors.black.withAlpha(100)),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                onSelectedItem(entry.node);
              },
              child: child,
            ),
          ),
        ],
      ),
    );

    if (decoration != null) {
      content = DecoratedBox(decoration: decoration!, child: content);
    }

    return ColoredBox(
      color: isSelected ? Colors.grey.withAlpha(100) : Colors.transparent,
      child: TreeIndentation(
        entry: entry,
        guide: IndentGuide.connectingLines(color: Colors.transparent, indent: 16),
        child: content,
      ),
    );
  }
}
