
import 'package:flutter_fancy_tree_view2/flutter_fancy_tree_view2.dart';
import 'package:zxb/component/tree/tree_item.dart';

extension TreeDragAndDropDetailsExt on TreeDragAndDropDetails {
  T mapDropPosition<T>({required T Function() whenAbove, required T Function() whenInside, required T Function() whenBelow}) {
    final double oneThirdOfTotalHeight = targetBounds.height * 0.3;
    final double pointerVerticalOffset = dropPosition.dy;

    if (pointerVerticalOffset < oneThirdOfTotalHeight) {
      return whenAbove();
    } else if (pointerVerticalOffset < oneThirdOfTotalHeight * 2) {
      return whenInside();
    } else {
      return whenBelow();
    }
  }
}