import 'package:flutter/material.dart';

class Attachment extends StatefulWidget {
  const Attachment({super.key});

  @override
  State<StatefulWidget> createState() => _AttachmentState();
}

class _AttachmentState extends State<Attachment> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children:
          <Widget>[
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(color: Theme.of(context).colorScheme.primaryContainer, borderRadius: BorderRadius.circular(8)),
              child: Icon(Icons.add,size: 36,),
            ),
          ] +
          <Widget>[],
    );
  }
}
