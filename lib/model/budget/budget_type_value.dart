import 'package:collection/collection.dart';
import 'package:get/get.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/model/budget/budget_item_value.dart';

class BudgetTypeValue {
  late String id;
  late String name;
  late int index;
  final count = RxInt(0);
  final totalAmount = RxDouble(0);
  final spent = RxDouble(0);
  final left = RxDouble(0);
  final children = RxList<BudgetItemValue>();

  final _workers = <String, Worker>{};

  BudgetTypeValue(TbBudgetType type) {
    id = type.id!;
    name = type.name;
    index = type.index!;
  }

  void init() {
    _summary();
    for (var item in children) {
      _workers["${item.id}_totalAmount"] = ever(item.totalAmount, (_) => _summary());
      _workers["${item.id}_spent"] = ever(item.spent, (_) => _summary());
    }
  }

  void _summary() {
    count.value = children.length;
    totalAmount.value = children.isEmpty ? 0 : children.map((v) => v.totalAmount.value!).reduce((a, b) => a + b);
    spent.value = children.isEmpty ? 0 : children.map((v) => v.spent.value!).reduce((a, b) => a + b);
    left.value = totalAmount.value - spent.value;
  }

  void appendChild(BudgetItemValue result) {
    result.spent.value = result.paymentRecords.isEmpty ? 0 : result.paymentRecords.map((v) => v.amount.value).reduce((a, b) => a + b);
    result.index.value = (children.map((v) => v.index.value).maxOrNull ?? 0) + 1;
    children.add(result);
    _summary();
    _workers["${result.id}_totalAmount"] = ever(result.totalAmount, (_) => _summary());
    _workers["${result.id}_spent"] = ever(result.spent, (_) => _summary());
  }

  void removeChild(BudgetItemValue result) {
    _workers.remove("${result.id}_totalAmount");
    _workers.remove("${result.id}_spent");
    children.remove(result);
    _summary();
  }
}
