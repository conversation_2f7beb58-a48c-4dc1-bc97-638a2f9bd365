import 'dart:convert';

import 'package:built_collection/built_collection.dart';
import 'package:get/get.dart';
import 'package:openapi/openapi.dart';
import 'package:zxb/model/budget/budget_payment_value.dart';
import 'package:zxb/until/id.dart';

class BudgetItemValue {
  late String id;
  late String budgetId;
  Rx<String> title = Rx<String>("");
  RxInt index = RxInt(0);
  RxnString mainType = RxnString();
  RxnString type = RxnString();
  RxnDouble totalAmount = RxnDouble();
  RxnDouble spent = RxnDouble();
  RxnString contactName = RxnString();
  RxnString contactPhone = RxnString();
  RxnString url = RxnString();
  RxnInt sizeWidth = RxnInt();
  RxnInt sizeDepth = RxnInt();
  RxnInt sizeHeight = RxnInt();
  RxnString description = RxnString();
  final paymentRecords = RxList<BudgetPaymentValue>();

  final TbBudgetItem? item;

  BudgetItemValue(this.item) {
    id = item?.id ?? generateId();
    budgetId = item?.budgetId ?? "";
    title.value = item?.title ?? "";
    index.value = item?.index ?? 0;
    type.value = item?.type;
    totalAmount.value = item?.lineAmount;
    contactName.value = item?.contactName;
    contactPhone.value = item?.contactName;
    sizeWidth.value = item?.sizeWidth;
    sizeHeight.value = item?.sizeHeight;
    sizeDepth.value = item?.sizeDepth;
    description.value = item?.description;
    paymentRecords.value =
        item?.paymentRecords?.map((v) {
          final j = jsonDecode(v);
          return BudgetPaymentValue(
            (BudgetPaymentBuilder()
                  ..title = j["title"]
                  ..amount = j["amount"]
                  ..time = DateTime.parse(j["time"]))
                .build(),
          );
        }).toList() ??
        [];
    spent.value = paymentRecords.isEmpty ? 0 : paymentRecords.map((v) => v.amount.value).reduce((a, b) => a + b);
  }

  BudgetItemValue copy() {
    return BudgetItemValue(item)
      ..id = id
      ..budgetId = budgetId
      ..title.value = title.value
      ..index.value = index.value
      ..type.value = type.value
      ..totalAmount.value = totalAmount.value
      ..contactName.value = contactName.value
      ..contactPhone.value = contactPhone.value
      ..sizeWidth.value = sizeWidth.value
      ..sizeHeight.value = sizeHeight.value
      ..sizeDepth.value = sizeDepth.value
      ..description.value = description.value
      ..paymentRecords.value = paymentRecords.map((v) => v.copy()).toList()
      ..spent.value = spent.value;
  }

  void mergeFrom(BudgetItemValue item) {
    title.value = item.title.value;
    index.value = item.index.value;
    type.value = item.type.value;
    totalAmount.value = item.totalAmount.value;
    contactName.value = item.contactName.value;
    contactPhone.value = item.contactPhone.value;
    sizeWidth.value = item.sizeWidth.value;
    sizeHeight.value = item.sizeHeight.value;
    sizeDepth.value = item.sizeDepth.value;
    description.value = item.description.value;
    paymentRecords.value = item.paymentRecords.toList();
    spent.value = paymentRecords.isEmpty ? 0 : paymentRecords.map((v) => v.amount.value).reduce((a, b) => a + b);
  }

  TbBudgetItem toDb() {
    return (TbBudgetItemBuilder()
          ..id = id
          ..budgetId = budgetId
          ..title = title.value
          ..index = index.value
          ..type = type.value
          ..lineAmount = totalAmount.value
          ..contactName = contactName.value
          ..contactPhone = contactPhone.value
          ..sizeDepth = sizeDepth.value
          ..sizeHeight = sizeHeight.value
          ..sizeWidth = sizeWidth.value
          ..description = description.value
          ..paymentRecords = ListBuilder(paymentRecords.map((p) => p.toDb())))
        .build();
  }
}
