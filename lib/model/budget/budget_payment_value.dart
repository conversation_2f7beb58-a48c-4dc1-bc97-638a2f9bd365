
import 'dart:convert';

import 'package:get/get.dart';
import 'package:openapi/openapi.dart';

class BudgetPaymentValue {
  final title = RxnString();
  final amount = RxDouble(0);
  final time = Rx<DateTime>(DateTime.now());
  final BudgetPayment? item;

  BudgetPaymentValue(this.item) {
    title.value = item?.title;
    amount.value = item?.amount ?? 0;
    time.value = item?.time ?? DateTime.now();
  }

  String toDb() {
    return jsonEncode({"title": title.value, "amount": amount.value, "time": time.value.toIso8601String()});
  }

  BudgetPaymentValue copy() {
    return BudgetPaymentValue(item)
      ..amount.value = amount.value
      ..time.value = time.value
      ..title.value = title.value;
  }
}
