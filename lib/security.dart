import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

const _tokenKey = "jwt_token";

Future<String?> getToken() async {
  return (await SharedPreferences.getInstance()).getString(_tokenKey);
}

Future<void> setToken(String token) async {
  (await SharedPreferences.getInstance()).setString(_tokenKey, token);
}

Future<void> removeToken() async {
  (await SharedPreferences.getInstance()).remove(_tokenKey);
}

class AuthenticationInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = (await SharedPreferences.getInstance()).getString(_tokenKey);
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    super.onRequest(options, handler);
  }
}
