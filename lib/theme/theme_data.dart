import 'package:flutter/material.dart';
import 'package:zxb/theme/color_schema.dart';
import 'package:zxb/theme/text_theme.dart';

final lightTheme = ThemeData(
  colorScheme: lightColor,
  inputDecorationTheme: InputDecorationTheme(
    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 10).copyWith(right: 10),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.grey.shade500, width: 1),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.black, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: lightColor.errorContainer, width: 2),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: lightColor.error, width: 2),
    ),
    isDense: true,
  ),
  textTheme: lightText,
  filledButtonTheme: FilledButtonThemeData(
    style: ButtonStyle(
      backgroundColor: WidgetStatePropertyAll(lightColor.primary),
      textStyle: WidgetStatePropertyAll(lightText.titleSmall?.copyWith(fontWeight: FontWeight.w600)),
      minimumSize: WidgetStatePropertyAll(Size(10, 44)),
    ),
  ),
  dividerTheme: DividerThemeData(color: lightColor.outlineVariant, thickness: 1),
  progressIndicatorTheme: ProgressIndicatorThemeData(circularTrackColor: lightColor.outlineVariant, strokeCap: StrokeCap.round, strokeWidth: 3),
  tabBarTheme: TabBarThemeData(),
);
